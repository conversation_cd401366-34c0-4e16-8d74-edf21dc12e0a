﻿#pragma checksum "..\..\Cap_Individual.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "E412F3307E5B0FE090B4CF3767044F023C3367B8A09B85ABB896DEFD610F6985"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CapBankAutomation {
    
    
    /// <summary>
    /// Cap_Individual
    /// </summary>
    public partial class Cap_Individual : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 8 "..\..\Cap_Individual.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Q1;
        
        #line default
        #line hidden
        
        
        #line 12 "..\..\Cap_Individual.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Q2;
        
        #line default
        #line hidden
        
        
        #line 16 "..\..\Cap_Individual.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Q3;
        
        #line default
        #line hidden
        
        
        #line 20 "..\..\Cap_Individual.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Q4;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\Cap_Individual.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Q5;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\Cap_Individual.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CapacitorNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\Cap_Individual.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPowerLabel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CapBankAutomation;component/cap_individual.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\Cap_Individual.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Q1 = ((System.Windows.Controls.Image)(target));
            
            #line 8 "..\..\Cap_Individual.xaml"
            this.Q1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Switch_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.Q2 = ((System.Windows.Controls.Image)(target));
            
            #line 12 "..\..\Cap_Individual.xaml"
            this.Q2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Switch_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 3:
            this.Q3 = ((System.Windows.Controls.Image)(target));
            
            #line 16 "..\..\Cap_Individual.xaml"
            this.Q3.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Switch_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 4:
            this.Q4 = ((System.Windows.Controls.Image)(target));
            
            #line 20 "..\..\Cap_Individual.xaml"
            this.Q4.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Switch_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 5:
            this.Q5 = ((System.Windows.Controls.Image)(target));
            
            #line 24 "..\..\Cap_Individual.xaml"
            this.Q5.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Switch_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CapacitorNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TotalPowerLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

