namespace CapBankAutomation
{
    public static class CalculationHelper
    {
        public static double CalculateQPower(bool[] switchStates)
        {
            double power = 0;
            if (switchStates == null || switchStates.Length != 5) return 0;

            if (switchStates[0]) power += 0.1;
            if (switchStates[1]) power += 0.2;
            if (switchStates[2]) power += 0.8;
            if (switchStates[3]) power += 1.2;
            if (switchStates[4]) power += 1.6;
            return power;
        }
    }
}
