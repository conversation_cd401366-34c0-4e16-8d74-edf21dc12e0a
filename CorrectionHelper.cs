﻿using System;
using System.Linq;
using System.Windows;
using System.Globalization;
using System.Windows.Controls;

namespace CapBankAutomation
{
    public class CorrectionHelper
    {
        private readonly MainWindow _mainWindow;

        public CorrectionHelper(MainWindow mainWindow)
        {
            _mainWindow = mainWindow;
        }

        public void CorrectPower()
        {
            // Usar cultura invariante para garantir que '.' seja o separador decimal
            CultureInfo ci = CultureInfo.InvariantCulture;

            if (!(_mainWindow.cmbVoltageLevel.SelectedItem is ComboBoxItem selectedVoltageItem) ||
                selectedVoltageItem.Content == null ||
                !double.TryParse(_mainWindow.txtTestVoltage.Text.Replace(",", "."), NumberStyles.Any, ci, out double testVoltage) ||
                !double.TryParse(_mainWindow.txtTotalPower.Text.Replace(",", "."), NumberStyles.Any, ci, out double desiredPower))
            {
                MessageBox.Show("Por favor, verifique os campos de tensão de teste e potência total desejada.", "Erro de entrada", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            string voltageString = selectedVoltageItem.Content.ToString().Replace(" kV", "").Trim();
            if (!double.TryParse(voltageString, NumberStyles.Any, ci, out double selectedVoltageKV))
            {
                 MessageBox.Show("Valor de tensão do seletor é inválido.", "Erro de entrada", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            if (selectedVoltageKV == 0)
            {
                MessageBox.Show("A tensão de nível selecionada não pode ser zero.", "Erro de Divisão", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            double voltageRatio = testVoltage / selectedVoltageKV;
            double testPower = desiredPower / Math.Pow(voltageRatio, 2);

            var (minPower, maxPower) = GetAvailablePowerFromVoltageLevel(selectedVoltageKV.ToString(ci));

            if (testPower < minPower || testPower > maxPower)
            {
                MessageBox.Show($"A potência de teste calculada de {testPower:F2} MVAr está fora dos limites permitidos ({minPower:F2} MVAr a {maxPower:F2} MVAr) para a tensão de {selectedVoltageKV} kV.", "Erro de Potência", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Atualiza a potência desejada diretamente no campo de texto antes de mostrar a mensagem
            _mainWindow.Dispatcher.Invoke(() => {
                _mainWindow.txtTotalPower.Text = testPower.ToString("F2", ci);
            });
            
            string message = $"A potência de teste necessária para o teste deve ser {testPower:F2} MVAr. O valor no campo 'Potência Desejada' foi ajustado. Clique em 'Sugerir' novamente para obter a nova configuração.";
            MessageBox.Show(message, "Correção de Potência de Teste", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private (double MinPower, double MaxPower) GetAvailablePowerFromVoltageLevel(string voltageLevel)
        {
            switch (voltageLevel)
            {
                case "13.8": return (0.3, 23.4);
                case "23.9": return (0.3, 23.4);
                case "27.6": return (1.2, 93.6);
                case "41.4": return (0.9, 70.2);
                case "47.8": return (1.2, 93.6);
                case "55.2": return (1.2, 93.6);
                case "71.7": return (0.9, 70.2);
                case "95.6": return (1.2, 93.6);
                default: return (0, 0);
            }
        }
    }
}