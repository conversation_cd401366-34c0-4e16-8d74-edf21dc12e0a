﻿#pragma checksum "..\..\MainWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "03DB813846BE8971CEDDB9929CAC1916635CF52A94B07D3ACBCD6C3ABEA5151D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CapBankAutomation {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 93 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbCircuitType;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbVoltageLevel;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTotalPower;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSuggest;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnVerify;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtResult;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl ResultItemsControl;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTestVoltage;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCorrection;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClear;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar VerifyProgressBar;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas MyCanvas;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS2A1;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS1A2;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS1B2;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS3C2;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS6B;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CS_4A1;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CS_3A1;
        
        #line default
        #line hidden
        
        
        #line 320 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CS_3A2;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_BARRA_ENTRADA_CS_2A1_E_CS_2A2;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CS_4A2;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CS_3A3;
        
        #line default
        #line hidden
        
        
        #line 328 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_SAIDA_CS_2A1;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_SAIDA_CS_2A2;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_SAIDA_CP_1A1;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_BARRA_SAIDA_CS_7A;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_SAIDA_CS_7A;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_SAIDA_CS_4A1;
        
        #line default
        #line hidden
        
        
        #line 340 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CS_1A1;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CS_1A2;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CS_7A;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_SAIDA_CS_3A1;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CS_6A;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_ENTRADA_CS_6B;
        
        #line default
        #line hidden
        
        
        #line 352 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_BARRA_SAIDA_CS_3A1_ATE_CS_1A2;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_BARRA_ENTRADA_CS_2B1_ATE_CS_2B2;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_ENTRADA_CS_4B1;
        
        #line default
        #line hidden
        
        
        #line 358 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_ENTRADA_CS_3B1;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_ENTRADA_CS_4B2_;
        
        #line default
        #line hidden
        
        
        #line 362 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_ENTRADA_CS_3B2;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_BARRA_SAIDA_CS_3B1_A_CS_1B2;
        
        #line default
        #line hidden
        
        
        #line 366 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_SAIDA_CP_1B1;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_BARRA_SAIDA_CS_7B;
        
        #line default
        #line hidden
        
        
        #line 370 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_ENTRADA_CS_7B;
        
        #line default
        #line hidden
        
        
        #line 372 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_SAIDA_7B;
        
        #line default
        #line hidden
        
        
        #line 374 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_SAIDA_CS_4B1;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_SAIDA_CS_4A2;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_SAIDA_CS_2B1;
        
        #line default
        #line hidden
        
        
        #line 380 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_ENTRADA_CS_1B1;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_ENTRADA_CS_1B2;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_ENTRADA_CS_1C2;
        
        #line default
        #line hidden
        
        
        #line 386 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_ENTRADA_CS_1C1;
        
        #line default
        #line hidden
        
        
        #line 388 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_SAIDA_CS_3B1;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_ENTRADA_CS_4C1;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_BARRA_ENTRADA_CS_2C1_E_CS_2C2;
        
        #line default
        #line hidden
        
        
        #line 394 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_ENTRADA_CS_3C1;
        
        #line default
        #line hidden
        
        
        #line 396 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_SAIDA_CS_4C1;
        
        #line default
        #line hidden
        
        
        #line 398 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_SAIDA_CS_4B2;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_SAIDA_CS_2C2;
        
        #line default
        #line hidden
        
        
        #line 402 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_SAIDA_2C1;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_ENTRADA_CS_4C2;
        
        #line default
        #line hidden
        
        
        #line 406 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_ENTRADA_CS_3C2;
        
        #line default
        #line hidden
        
        
        #line 408 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_SAIDA_CS_2B2;
        
        #line default
        #line hidden
        
        
        #line 410 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_SAIDA_CS_4C2;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_BARRA_SAIDA_CS_7C;
        
        #line default
        #line hidden
        
        
        #line 414 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_BARRA_SAIDA_CS_3C1_A_CS_1C2;
        
        #line default
        #line hidden
        
        
        #line 416 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_ENTRADA_CP_4A2;
        
        #line default
        #line hidden
        
        
        #line 418 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_BARRA_SAIDA_CS_7C_ATE_CP_4A2;
        
        #line default
        #line hidden
        
        
        #line 420 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_ENTRADA_CS_7C;
        
        #line default
        #line hidden
        
        
        #line 422 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_SAIDA_CP_1C1;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_SAIDA_CS_7C;
        
        #line default
        #line hidden
        
        
        #line 426 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_SAIDA_CS_3C1;
        
        #line default
        #line hidden
        
        
        #line 428 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line BARRA_DE_NEUTRO__ENTRADA_CS_C6;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_SAIDA_CS_6B;
        
        #line default
        #line hidden
        
        
        #line 433 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPowerLabel;
        
        #line default
        #line hidden
        
        
        #line 438 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_B_SAIDA_TC_2;
        
        #line default
        #line hidden
        
        
        #line 440 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_C_SAIDA_TC_3;
        
        #line default
        #line hidden
        
        
        #line 442 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Line FASE_A_SAIDA_TC_1;
        
        #line default
        #line hidden
        
        
        #line 447 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CTAValue;
        
        #line default
        #line hidden
        
        
        #line 451 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CTBValue;
        
        #line default
        #line hidden
        
        
        #line 455 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CTCValue;
        
        #line default
        #line hidden
        
        
        #line 458 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CSA;
        
        #line default
        #line hidden
        
        
        #line 462 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CSB;
        
        #line default
        #line hidden
        
        
        #line 466 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CSC;
        
        #line default
        #line hidden
        
        
        #line 471 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS6C;
        
        #line default
        #line hidden
        
        
        #line 492 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP4C2;
        
        #line default
        #line hidden
        
        
        #line 494 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP3C1;
        
        #line default
        #line hidden
        
        
        #line 496 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP3C2;
        
        #line default
        #line hidden
        
        
        #line 498 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP2C1;
        
        #line default
        #line hidden
        
        
        #line 500 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP2C2;
        
        #line default
        #line hidden
        
        
        #line 502 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP1C1;
        
        #line default
        #line hidden
        
        
        #line 504 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP1C2;
        
        #line default
        #line hidden
        
        
        #line 506 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP3A1;
        
        #line default
        #line hidden
        
        
        #line 508 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP1A1;
        
        #line default
        #line hidden
        
        
        #line 510 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP2A1;
        
        #line default
        #line hidden
        
        
        #line 512 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP4C1;
        
        #line default
        #line hidden
        
        
        #line 514 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP4B1;
        
        #line default
        #line hidden
        
        
        #line 516 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP4B2;
        
        #line default
        #line hidden
        
        
        #line 518 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP3B1;
        
        #line default
        #line hidden
        
        
        #line 520 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP3B2;
        
        #line default
        #line hidden
        
        
        #line 522 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP2B1;
        
        #line default
        #line hidden
        
        
        #line 524 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP2B2;
        
        #line default
        #line hidden
        
        
        #line 526 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP1B1;
        
        #line default
        #line hidden
        
        
        #line 528 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP1B2;
        
        #line default
        #line hidden
        
        
        #line 530 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP4A2;
        
        #line default
        #line hidden
        
        
        #line 532 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP3A2;
        
        #line default
        #line hidden
        
        
        #line 534 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP2A2;
        
        #line default
        #line hidden
        
        
        #line 536 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP1A2;
        
        #line default
        #line hidden
        
        
        #line 540 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS4A1;
        
        #line default
        #line hidden
        
        
        #line 544 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS3A1;
        
        #line default
        #line hidden
        
        
        #line 546 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS1A1;
        
        #line default
        #line hidden
        
        
        #line 548 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS4C1;
        
        #line default
        #line hidden
        
        
        #line 550 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS4C2;
        
        #line default
        #line hidden
        
        
        #line 552 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS3C1;
        
        #line default
        #line hidden
        
        
        #line 556 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS2C1;
        
        #line default
        #line hidden
        
        
        #line 558 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS2C2;
        
        #line default
        #line hidden
        
        
        #line 560 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS1C1;
        
        #line default
        #line hidden
        
        
        #line 562 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS1C2;
        
        #line default
        #line hidden
        
        
        #line 564 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS7C;
        
        #line default
        #line hidden
        
        
        #line 581 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS4B1;
        
        #line default
        #line hidden
        
        
        #line 583 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS4B2;
        
        #line default
        #line hidden
        
        
        #line 585 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS3B1;
        
        #line default
        #line hidden
        
        
        #line 587 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS3B2;
        
        #line default
        #line hidden
        
        
        #line 589 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS2B1;
        
        #line default
        #line hidden
        
        
        #line 591 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS2B2;
        
        #line default
        #line hidden
        
        
        #line 593 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS1B1;
        
        #line default
        #line hidden
        
        
        #line 595 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS7A;
        
        #line default
        #line hidden
        
        
        #line 603 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS7B;
        
        #line default
        #line hidden
        
        
        #line 611 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS6A;
        
        #line default
        #line hidden
        
        
        #line 619 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS4A2;
        
        #line default
        #line hidden
        
        
        #line 621 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS3A2;
        
        #line default
        #line hidden
        
        
        #line 623 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CS2A2;
        
        #line default
        #line hidden
        
        
        #line 625 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image CP4A1;
        
        #line default
        #line hidden
        
        
        #line 627 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Data_Logo;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CapBankAutomation;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.cmbCircuitType = ((System.Windows.Controls.ComboBox)(target));
            
            #line 93 "..\..\MainWindow.xaml"
            this.cmbCircuitType.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbCircuitType_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.cmbVoltageLevel = ((System.Windows.Controls.ComboBox)(target));
            
            #line 103 "..\..\MainWindow.xaml"
            this.cmbVoltageLevel.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbVoltageLevel_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.txtTotalPower = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.btnSuggest = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\MainWindow.xaml"
            this.btnSuggest.Click += new System.Windows.RoutedEventHandler(this.BtnSuggest_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnVerify = ((System.Windows.Controls.Button)(target));
            
            #line 126 "..\..\MainWindow.xaml"
            this.btnVerify.Click += new System.Windows.RoutedEventHandler(this.BtnVerify_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.txtResult = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ResultItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 9:
            this.txtTestVoltage = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.btnCorrection = ((System.Windows.Controls.Button)(target));
            
            #line 172 "..\..\MainWindow.xaml"
            this.btnCorrection.Click += new System.Windows.RoutedEventHandler(this.btnCorrection_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnClear = ((System.Windows.Controls.Button)(target));
            
            #line 176 "..\..\MainWindow.xaml"
            this.btnClear.Click += new System.Windows.RoutedEventHandler(this.BtnClear_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.VerifyProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 13:
            this.MyCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 14:
            this.CS2A1 = ((System.Windows.Controls.Image)(target));
            
            #line 249 "..\..\MainWindow.xaml"
            this.CS2A1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 15:
            this.CS1A2 = ((System.Windows.Controls.Image)(target));
            
            #line 257 "..\..\MainWindow.xaml"
            this.CS1A2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 16:
            this.CS1B2 = ((System.Windows.Controls.Image)(target));
            
            #line 275 "..\..\MainWindow.xaml"
            this.CS1B2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 17:
            this.CS3C2 = ((System.Windows.Controls.Image)(target));
            
            #line 285 "..\..\MainWindow.xaml"
            this.CS3C2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 18:
            this.CS6B = ((System.Windows.Controls.Image)(target));
            
            #line 305 "..\..\MainWindow.xaml"
            this.CS6B.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 19:
            this.FASE_A_ENTRADA_CS_4A1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 20:
            this.FASE_A_ENTRADA_CS_3A1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 21:
            this.FASE_A_ENTRADA_CS_3A2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 22:
            this.FASE_A_BARRA_ENTRADA_CS_2A1_E_CS_2A2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 23:
            this.FASE_A_ENTRADA_CS_4A2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 24:
            this.FASE_A_ENTRADA_CS_3A3 = ((System.Windows.Shapes.Line)(target));
            return;
            case 25:
            this.FASE_A_SAIDA_CS_2A1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 26:
            this.FASE_A_SAIDA_CS_2A2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 27:
            this.FASE_A_SAIDA_CP_1A1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 28:
            this.FASE_A_BARRA_SAIDA_CS_7A = ((System.Windows.Shapes.Line)(target));
            return;
            case 29:
            this.FASE_A_SAIDA_CS_7A = ((System.Windows.Shapes.Line)(target));
            return;
            case 30:
            this.FASE_A_SAIDA_CS_4A1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 31:
            this.FASE_A_ENTRADA_CS_1A1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 32:
            this.FASE_A_ENTRADA_CS_1A2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 33:
            this.FASE_A_ENTRADA_CS_7A = ((System.Windows.Shapes.Line)(target));
            return;
            case 34:
            this.FASE_A_SAIDA_CS_3A1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 35:
            this.FASE_A_ENTRADA_CS_6A = ((System.Windows.Shapes.Line)(target));
            return;
            case 36:
            this.FASE_B_ENTRADA_CS_6B = ((System.Windows.Shapes.Line)(target));
            return;
            case 37:
            this.FASE_A_BARRA_SAIDA_CS_3A1_ATE_CS_1A2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 38:
            this.FASE_B_BARRA_ENTRADA_CS_2B1_ATE_CS_2B2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 39:
            this.FASE_B_ENTRADA_CS_4B1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 40:
            this.FASE_B_ENTRADA_CS_3B1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 41:
            this.FASE_B_ENTRADA_CS_4B2_ = ((System.Windows.Shapes.Line)(target));
            return;
            case 42:
            this.FASE_B_ENTRADA_CS_3B2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 43:
            this.FASE_B_BARRA_SAIDA_CS_3B1_A_CS_1B2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 44:
            this.FASE_B_SAIDA_CP_1B1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 45:
            this.FASE_B_BARRA_SAIDA_CS_7B = ((System.Windows.Shapes.Line)(target));
            return;
            case 46:
            this.FASE_B_ENTRADA_CS_7B = ((System.Windows.Shapes.Line)(target));
            return;
            case 47:
            this.FASE_B_SAIDA_7B = ((System.Windows.Shapes.Line)(target));
            return;
            case 48:
            this.FASE_B_SAIDA_CS_4B1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 49:
            this.FASE_A_SAIDA_CS_4A2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 50:
            this.FASE_B_SAIDA_CS_2B1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 51:
            this.FASE_B_ENTRADA_CS_1B1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 52:
            this.FASE_B_ENTRADA_CS_1B2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 53:
            this.FASE_C_ENTRADA_CS_1C2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 54:
            this.FASE_C_ENTRADA_CS_1C1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 55:
            this.FASE_B_SAIDA_CS_3B1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 56:
            this.FASE_C_ENTRADA_CS_4C1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 57:
            this.FASE_C_BARRA_ENTRADA_CS_2C1_E_CS_2C2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 58:
            this.FASE_C_ENTRADA_CS_3C1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 59:
            this.FASE_C_SAIDA_CS_4C1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 60:
            this.FASE_B_SAIDA_CS_4B2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 61:
            this.FASE_C_SAIDA_CS_2C2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 62:
            this.FASE_C_SAIDA_2C1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 63:
            this.FASE_C_ENTRADA_CS_4C2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 64:
            this.FASE_C_ENTRADA_CS_3C2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 65:
            this.FASE_B_SAIDA_CS_2B2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 66:
            this.FASE_C_SAIDA_CS_4C2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 67:
            this.FASE_C_BARRA_SAIDA_CS_7C = ((System.Windows.Shapes.Line)(target));
            return;
            case 68:
            this.FASE_C_BARRA_SAIDA_CS_3C1_A_CS_1C2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 69:
            this.FASE_A_ENTRADA_CP_4A2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 70:
            this.FASE_C_BARRA_SAIDA_CS_7C_ATE_CP_4A2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 71:
            this.FASE_C_ENTRADA_CS_7C = ((System.Windows.Shapes.Line)(target));
            return;
            case 72:
            this.FASE_C_SAIDA_CP_1C1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 73:
            this.FASE_C_SAIDA_CS_7C = ((System.Windows.Shapes.Line)(target));
            return;
            case 74:
            this.FASE_C_SAIDA_CS_3C1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 75:
            this.BARRA_DE_NEUTRO__ENTRADA_CS_C6 = ((System.Windows.Shapes.Line)(target));
            return;
            case 76:
            this.FASE_C_SAIDA_CS_6B = ((System.Windows.Shapes.Line)(target));
            return;
            case 77:
            this.TotalPowerLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 78:
            this.FASE_B_SAIDA_TC_2 = ((System.Windows.Shapes.Line)(target));
            return;
            case 79:
            this.FASE_C_SAIDA_TC_3 = ((System.Windows.Shapes.Line)(target));
            return;
            case 80:
            this.FASE_A_SAIDA_TC_1 = ((System.Windows.Shapes.Line)(target));
            return;
            case 81:
            this.CTAValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 82:
            this.CTBValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 83:
            this.CTCValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 84:
            this.CSA = ((System.Windows.Controls.Image)(target));
            
            #line 458 "..\..\MainWindow.xaml"
            this.CSA.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 85:
            this.CSB = ((System.Windows.Controls.Image)(target));
            
            #line 462 "..\..\MainWindow.xaml"
            this.CSB.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 86:
            this.CSC = ((System.Windows.Controls.Image)(target));
            
            #line 466 "..\..\MainWindow.xaml"
            this.CSC.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 87:
            this.CS6C = ((System.Windows.Controls.Image)(target));
            
            #line 471 "..\..\MainWindow.xaml"
            this.CS6C.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 88:
            this.CP4C2 = ((System.Windows.Controls.Image)(target));
            
            #line 492 "..\..\MainWindow.xaml"
            this.CP4C2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 89:
            this.CP3C1 = ((System.Windows.Controls.Image)(target));
            
            #line 494 "..\..\MainWindow.xaml"
            this.CP3C1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 90:
            this.CP3C2 = ((System.Windows.Controls.Image)(target));
            
            #line 496 "..\..\MainWindow.xaml"
            this.CP3C2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 91:
            this.CP2C1 = ((System.Windows.Controls.Image)(target));
            
            #line 498 "..\..\MainWindow.xaml"
            this.CP2C1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 92:
            this.CP2C2 = ((System.Windows.Controls.Image)(target));
            
            #line 500 "..\..\MainWindow.xaml"
            this.CP2C2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 93:
            this.CP1C1 = ((System.Windows.Controls.Image)(target));
            
            #line 502 "..\..\MainWindow.xaml"
            this.CP1C1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 94:
            this.CP1C2 = ((System.Windows.Controls.Image)(target));
            
            #line 504 "..\..\MainWindow.xaml"
            this.CP1C2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 95:
            this.CP3A1 = ((System.Windows.Controls.Image)(target));
            
            #line 506 "..\..\MainWindow.xaml"
            this.CP3A1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 96:
            this.CP1A1 = ((System.Windows.Controls.Image)(target));
            
            #line 508 "..\..\MainWindow.xaml"
            this.CP1A1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 97:
            this.CP2A1 = ((System.Windows.Controls.Image)(target));
            
            #line 510 "..\..\MainWindow.xaml"
            this.CP2A1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 98:
            this.CP4C1 = ((System.Windows.Controls.Image)(target));
            
            #line 512 "..\..\MainWindow.xaml"
            this.CP4C1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 99:
            this.CP4B1 = ((System.Windows.Controls.Image)(target));
            
            #line 514 "..\..\MainWindow.xaml"
            this.CP4B1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 100:
            this.CP4B2 = ((System.Windows.Controls.Image)(target));
            
            #line 516 "..\..\MainWindow.xaml"
            this.CP4B2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 101:
            this.CP3B1 = ((System.Windows.Controls.Image)(target));
            
            #line 518 "..\..\MainWindow.xaml"
            this.CP3B1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 102:
            this.CP3B2 = ((System.Windows.Controls.Image)(target));
            
            #line 520 "..\..\MainWindow.xaml"
            this.CP3B2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 103:
            this.CP2B1 = ((System.Windows.Controls.Image)(target));
            
            #line 522 "..\..\MainWindow.xaml"
            this.CP2B1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 104:
            this.CP2B2 = ((System.Windows.Controls.Image)(target));
            
            #line 524 "..\..\MainWindow.xaml"
            this.CP2B2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 105:
            this.CP1B1 = ((System.Windows.Controls.Image)(target));
            
            #line 526 "..\..\MainWindow.xaml"
            this.CP1B1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 106:
            this.CP1B2 = ((System.Windows.Controls.Image)(target));
            
            #line 528 "..\..\MainWindow.xaml"
            this.CP1B2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 107:
            this.CP4A2 = ((System.Windows.Controls.Image)(target));
            
            #line 530 "..\..\MainWindow.xaml"
            this.CP4A2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 108:
            this.CP3A2 = ((System.Windows.Controls.Image)(target));
            
            #line 532 "..\..\MainWindow.xaml"
            this.CP3A2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 109:
            this.CP2A2 = ((System.Windows.Controls.Image)(target));
            
            #line 534 "..\..\MainWindow.xaml"
            this.CP2A2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 110:
            this.CP1A2 = ((System.Windows.Controls.Image)(target));
            
            #line 536 "..\..\MainWindow.xaml"
            this.CP1A2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 111:
            this.CS4A1 = ((System.Windows.Controls.Image)(target));
            
            #line 540 "..\..\MainWindow.xaml"
            this.CS4A1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 112:
            this.CS3A1 = ((System.Windows.Controls.Image)(target));
            
            #line 544 "..\..\MainWindow.xaml"
            this.CS3A1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 113:
            this.CS1A1 = ((System.Windows.Controls.Image)(target));
            
            #line 546 "..\..\MainWindow.xaml"
            this.CS1A1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 114:
            this.CS4C1 = ((System.Windows.Controls.Image)(target));
            
            #line 548 "..\..\MainWindow.xaml"
            this.CS4C1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 115:
            this.CS4C2 = ((System.Windows.Controls.Image)(target));
            
            #line 550 "..\..\MainWindow.xaml"
            this.CS4C2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 116:
            this.CS3C1 = ((System.Windows.Controls.Image)(target));
            
            #line 552 "..\..\MainWindow.xaml"
            this.CS3C1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 117:
            this.CS2C1 = ((System.Windows.Controls.Image)(target));
            
            #line 556 "..\..\MainWindow.xaml"
            this.CS2C1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 118:
            this.CS2C2 = ((System.Windows.Controls.Image)(target));
            
            #line 558 "..\..\MainWindow.xaml"
            this.CS2C2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 119:
            this.CS1C1 = ((System.Windows.Controls.Image)(target));
            
            #line 560 "..\..\MainWindow.xaml"
            this.CS1C1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 120:
            this.CS1C2 = ((System.Windows.Controls.Image)(target));
            
            #line 562 "..\..\MainWindow.xaml"
            this.CS1C2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 121:
            this.CS7C = ((System.Windows.Controls.Image)(target));
            
            #line 564 "..\..\MainWindow.xaml"
            this.CS7C.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 122:
            this.CS4B1 = ((System.Windows.Controls.Image)(target));
            
            #line 581 "..\..\MainWindow.xaml"
            this.CS4B1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 123:
            this.CS4B2 = ((System.Windows.Controls.Image)(target));
            
            #line 583 "..\..\MainWindow.xaml"
            this.CS4B2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 124:
            this.CS3B1 = ((System.Windows.Controls.Image)(target));
            
            #line 585 "..\..\MainWindow.xaml"
            this.CS3B1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 125:
            this.CS3B2 = ((System.Windows.Controls.Image)(target));
            
            #line 587 "..\..\MainWindow.xaml"
            this.CS3B2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 126:
            this.CS2B1 = ((System.Windows.Controls.Image)(target));
            
            #line 589 "..\..\MainWindow.xaml"
            this.CS2B1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 127:
            this.CS2B2 = ((System.Windows.Controls.Image)(target));
            
            #line 591 "..\..\MainWindow.xaml"
            this.CS2B2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 128:
            this.CS1B1 = ((System.Windows.Controls.Image)(target));
            
            #line 593 "..\..\MainWindow.xaml"
            this.CS1B1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 129:
            this.CS7A = ((System.Windows.Controls.Image)(target));
            
            #line 595 "..\..\MainWindow.xaml"
            this.CS7A.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 130:
            this.CS7B = ((System.Windows.Controls.Image)(target));
            
            #line 603 "..\..\MainWindow.xaml"
            this.CS7B.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 131:
            this.CS6A = ((System.Windows.Controls.Image)(target));
            
            #line 611 "..\..\MainWindow.xaml"
            this.CS6A.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 132:
            this.CS4A2 = ((System.Windows.Controls.Image)(target));
            
            #line 619 "..\..\MainWindow.xaml"
            this.CS4A2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 133:
            this.CS3A2 = ((System.Windows.Controls.Image)(target));
            
            #line 621 "..\..\MainWindow.xaml"
            this.CS3A2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 134:
            this.CS2A2 = ((System.Windows.Controls.Image)(target));
            
            #line 623 "..\..\MainWindow.xaml"
            this.CS2A2.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SwitchControl_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 135:
            this.CP4A1 = ((System.Windows.Controls.Image)(target));
            
            #line 625 "..\..\MainWindow.xaml"
            this.CP4A1.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Capacitor_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 136:
            this.Data_Logo = ((System.Windows.Controls.Image)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 8:
            
            #line 143 "..\..\MainWindow.xaml"
            ((System.Windows.Controls.RadioButton)(target)).Checked += new System.Windows.RoutedEventHandler(this.RadioButton_Checked);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

