﻿#pragma checksum "..\..\ConfigurationOptionsDialog.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "B313939A7E9C997955C3DF7855C794814DC89164E353AF70BB18266370F236DB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CapBankAutomation {
    
    
    /// <summary>
    /// ConfigurationOptionsDialog
    /// </summary>
    public partial class ConfigurationOptionsDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 41 "..\..\ConfigurationOptionsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MessageTextBlock;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\ConfigurationOptionsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Option1DescriptionTextBlock;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\ConfigurationOptionsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Option1Button;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\ConfigurationOptionsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Option2DescriptionTextBlock;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\ConfigurationOptionsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Option2Button;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CapBankAutomation;component/configurationoptionsdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\ConfigurationOptionsDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MessageTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.Option1DescriptionTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.Option1Button = ((System.Windows.Controls.Button)(target));
            
            #line 44 "..\..\ConfigurationOptionsDialog.xaml"
            this.Option1Button.Click += new System.Windows.RoutedEventHandler(this.Option1Button_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.Option2DescriptionTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.Option2Button = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\ConfigurationOptionsDialog.xaml"
            this.Option2Button.Click += new System.Windows.RoutedEventHandler(this.Option2Button_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

