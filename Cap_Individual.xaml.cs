﻿using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Imaging;

namespace CapBankAutomation
{
    public partial class Cap_Individual : Window
    {
        private double totalPower;
        private readonly BitmapImage openImage;
        private readonly BitmapImage closedImage;
        private readonly bool[] switchStates;
        private readonly TextBlock totalPowerLabel;

        public string CapacitorName { get; }
        public Action<string, bool[], double> CapacitorPowerSelected { get; set; }

        internal Dictionary<string, CapacitorState> _capacitorStates;

        public Cap_Individual(string capacitorName, double capacitorPower, bool[] initialSwitchStates, Dictionary<string, CapacitorState> capacitorStates)
        {
            InitializeComponent();
            openImage = new BitmapImage(new Uri("D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.1\\Img\\switch_open.png"));
            closedImage = new BitmapImage(new Uri("D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.1\\Img\\switch_closed.png"));
            
            CapacitorName = capacitorName;
            CapacitorNameTextBlock.Text = $"Capacitor: {capacitorName}";

            switchStates = initialSwitchStates;
            _capacitorStates = capacitorStates;

            totalPowerLabel = this.FindName("TotalPowerLabel") as TextBlock;

            UpdateTotalPowerLabel();
            UpdateSwitchImages();
        }

        

        private void Switch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Image img)
            {
                int switchIndex = GetSwitchIndex(img.Name);
                if (switchIndex >= 0)
                {
                    // Alterna o estado da chave.
                    switchStates[switchIndex] = !switchStates[switchIndex];
                    
                    // Atualiza a imagem da chave.
                    img.Source = switchStates[switchIndex] ? closedImage : openImage;

                    // Recalcula a potência e atualiza a UI.
                    UpdateTotalPowerLabel();

                    // Invoca o evento para notificar a MainWindow.
                    CapacitorPowerSelected?.Invoke(CapacitorName, switchStates, totalPower);
                }
            }
        }

        private int GetSwitchIndex(string switchName)
        {
            return switchName switch
            {
                "Q1" => 0,
                "Q2" => 1,
                "Q3" => 2,
                "Q4" => 3,
                "Q5" => 4,
                _ => -1,
            };
        }
        
        private void UpdateTotalPowerLabel()
        {
            totalPower = CalculationHelper.CalculateQPower(switchStates);
            if (totalPowerLabel != null)
            {
                totalPowerLabel.Text = $"Capacitor Total Power: {totalPower:F2} MVAr";
            }
        }

        private void UpdateSwitchImages()
        {
            Q1.Source = switchStates[0] ? closedImage : openImage;
            Q2.Source = switchStates[1] ? closedImage : openImage;
            Q3.Source = switchStates[2] ? closedImage : openImage;
            Q4.Source = switchStates[3] ? closedImage : openImage;
            Q5.Source = switchStates[4] ? closedImage : openImage;
        }
    }
}