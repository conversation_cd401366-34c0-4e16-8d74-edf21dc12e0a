﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace CapBankAutomation
{
    public class CapacitorColorManager
    {
        private readonly Dictionary<double, Color> powerColorMap = new Dictionary<double, Color>();
        private readonly Dictionary<double, SolidColorBrush> powerBrushMap = new Dictionary<double, SolidColorBrush>();
        private readonly List<Color> distinctColors;

        public CapacitorColorManager()
        {
            distinctColors = new List<Color>
            {
                Colors.SkyBlue,
                Colors.YellowGreen,
                Colors.Orange,
                Colors.MediumPurple,
                Colors.LightCoral,
                Colors.Turquoise,
                Colors.Gold,
                Colors.LightPink
            };
            GeneratePowerColorMap();
        }

        private void GeneratePowerColorMap()
        {
            // Gera um mapa de cores para potências comuns
            double[] commonPowers = { 0.1, 0.2, 0.3, 0.8, 0.9, 1.0, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.4, 2.7, 3.9 };
            for(int i = 0; i < commonPowers.Length; i++)
            {
                powerColorMap[commonPowers[i]] = distinctColors[i % distinctColors.Count];
            }
        }

        public Color GetColorForPower(double power)
        {
            power = Math.Round(power, 2);
            if (powerColorMap.TryGetValue(power, out Color color))
            {
                return color;
            }
            // Se não encontrar uma cor exata, retorna uma cor padrão.
            return Colors.Gray;
        }
        
        

        public SolidColorBrush GetBrushForPower(double power)
        {
            power = Math.Round(power, 2);
            if (!powerBrushMap.ContainsKey(power))
            {
                Color color = GetColorForPower(power);
                var brush = new SolidColorBrush(color);
                powerBrushMap[power] = brush;
                // Animação pode ser desativada ou simplificada se causar problemas de performance.
                // StartPulseAnimation(brush);
            }
            return powerBrushMap[power];
        }

        private void StartPulseAnimation(SolidColorBrush brush)
        {
            var opacityAnimation = new DoubleAnimation
            {
                From = 0.7,
                To = 1.0,
                Duration = TimeSpan.FromSeconds(1),
                AutoReverse = true,
                RepeatBehavior = RepeatBehavior.Forever
            };
            brush.BeginAnimation(SolidColorBrush.OpacityProperty, opacityAnimation);
        }
    }
}