﻿<Window
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
        xmlns:av="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="av" x:Class="CapBankAutomation.MainWindow"
        Title="Capacitor Bank Automation" Height="1110" Width="1858"
        WindowStartupLocation="CenterScreen"
        Background="#F7F9FC" WindowState="Maximized" Icon="/Img/Cap.png">
    <Window.Resources>
        <Style x:Key="HeaderTextBlockStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="FontSize" Value="24" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Foreground" Value="#34495E" />
            <Setter Property="Margin" Value="0,0,0,20" />
        </Style>

        <Style x:Key="SectionBorderStyle" TargetType="{x:Type Border}">
            <Setter Property="Background" Value="White" />
            <Setter Property="BorderBrush" Value="#E6EAF2" />
            <Setter Property="BorderThickness" Value="2" />
            <Setter Property="CornerRadius" Value="10" />
            <Setter Property="Padding" Value="20" />
            <Setter Property="Margin" Value="0,0,0,20" />
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect ShadowDepth="2" Direction="270" Color="#20000000" BlurRadius="4" Opacity="0.2" />
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="InputLabelStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Foreground" Value="#5D6D7E" />
            <Setter Property="Margin" Value="0,0,0,8" />
        </Style>

        <Style x:Key="ComboBoxStyle" TargetType="{x:Type ComboBox}">
            <Setter Property="Margin" Value="0,0,0,15" />
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="BorderBrush" Value="#D1D8E0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="FontSize" Value="14" />
        </Style>

        <Style x:Key="TextBoxStyle" TargetType="{x:Type TextBox}">
            <Setter Property="Margin" Value="0,0,0,15" />
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="BorderBrush" Value="#D1D8E0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="FontSize" Value="14" />
        </Style>

        <Style x:Key="ButtonStyle" TargetType="{x:Type Button}">
            <Setter Property="Foreground" Value="White" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Padding" Value="20,12" />
            <Setter Property="Margin" Value="0,8" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}" CornerRadius="6" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.9" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.8" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <Grid VerticalAlignment="Top" Margin="10,20,28,0" Height="1081">
        <Grid.ColumnDefinitions
>
            <ColumnDefinition
Width="350"/>
        </Grid.ColumnDefinitions>
        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="25,115,0,102" RenderTransformOrigin="0.518,0.532" HorizontalAlignment="Left" Width="290">
            <StackPanel>
                <TextBlock Text="Configurações" Style="{StaticResource HeaderTextBlockStyle}" />

                <Border Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <TextBlock Text="Tipo de Circuito:" Style="{StaticResource InputLabelStyle}" />
                        <ComboBox x:Name="cmbCircuitType" Style="{StaticResource ComboBoxStyle}" SelectionChanged="CmbCircuitType_SelectionChanged">
                            <ComboBoxItem Content="Trifásico"/>
                            <ComboBoxItem Content="Monofásico"/>
                        </ComboBox>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <TextBlock Text="Nível de Tensão (kV):" Style="{StaticResource InputLabelStyle}" />
                        <ComboBox x:Name="cmbVoltageLevel" Style="{StaticResource ComboBoxStyle}" SelectionChanged="CmbVoltageLevel_SelectionChanged">
                            <ComboBoxItem Content="13.8"/>
                            <ComboBoxItem Content="23.9"/>
                            <ComboBoxItem Content="27.6"/>
                            <ComboBoxItem Content="41.4"/>
                            <ComboBoxItem Content="47.8"/>
                            <ComboBoxItem Content="55.2"/>
                            <ComboBoxItem Content="71.7"/>
                            <ComboBoxItem Content="95.6"/>
                        </ComboBox>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <TextBlock Text="Potência Total Desejada (MVAr):" Style="{StaticResource InputLabelStyle}" />
                        <TextBox x:Name="txtTotalPower" Style="{StaticResource TextBoxStyle}" />
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <Button x:Name="btnSuggest" Content="Sugerir" Style="{StaticResource ButtonStyle}" Background="#4A90E2" Click="BtnSuggest_Click" />
                        <Button x:Name="btnVerify" Content="Verificar" Style="{StaticResource ButtonStyle}" Background="#F5B041" Click="BtnVerify_Click" />
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <TextBlock Text="Resultado:" Style="{StaticResource InputLabelStyle}" />
                        <TextBlock x:Name="txtResult" TextWrapping="Wrap" Margin="0,10,0,0" Foreground="#34495E" />
                        <ItemsControl x:Name="ResultItemsControl" Margin="0,15,0,0">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <ContentControl Content="{Binding}">
                                        <ContentControl.Style>
                                            <Style TargetType="{x:Type ContentControl}">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="{x:Type ContentControl}">
                                                            <RadioButton Content="{Binding}" GroupName="ResultOptions" Checked="RadioButton_Checked" 
                                                                Foreground="#34495E" Margin="0,5" />
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding SelectedItem.Content, ElementName=cmbCircuitType}" Value="Monofásico">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="{x:Type ContentControl}">
                                                                    <TextBlock Text="{Binding}" Foreground="#34495E" Margin="0,5" />
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ContentControl.Style>
                                    </ContentControl>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <Border Style="{StaticResource SectionBorderStyle}">
                    <StackPanel>
                        <TextBlock Text="Tensão de Teste (kV):" Style="{StaticResource InputLabelStyle}" />
                        <TextBox x:Name="txtTestVoltage" Style="{StaticResource TextBoxStyle}" />
                        <Button x:Name="btnCorrection" Content="Corrigir" Style="{StaticResource ButtonStyle}" Background="#27AE60" Click="btnCorrection_Click" />
                    </StackPanel>
                </Border>

                <Button x:Name="btnClear" Content="Limpar" Style="{StaticResource ButtonStyle}" Background="#E74C3C" Click="BtnClear_Click" />

                <ProgressBar x:Name="VerifyProgressBar" Visibility="Collapsed" Height="6" Margin="0,20" IsIndeterminate="True" 
                    Foreground="#4A90E2" Background="#E6EAF2" />

                <TextBlock Text="Desenvolvido por: AAS" Foreground="#95A5A6" HorizontalAlignment="Center" FontSize="12" Margin="0,25,0,0" />
            </StackPanel>
        </ScrollViewer>
        <Canvas
            Margin="435,141,-1406,354" x:Name="MyCanvas">
            <!-- Capacitores -->
            <!-- Banco 4 -->
            <TextBlock
                Canvas.Top="192" Text="CP 4A1" Canvas.Left="15" FontSize="10"/>
            <TextBlock
                Canvas.Top="192" Text="CP 4A2" Canvas.Left="245" FontSize="10"/>
            <TextBlock
                Canvas.Top="259" Text="CP 3A2" Canvas.Left="245" FontSize="10"/>
            <TextBlock
                Canvas.Top="395" Text="CP 2A1" Canvas.Left="15" FontSize="10"/>
            <TextBlock
                Canvas.Top="395" Text="CP 2A2" Canvas.Left="245" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CP 1A1" Canvas.Left="15" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CP 1A2" Canvas.Left="245" FontSize="10"/>
            <!-- Banco 5 -->
            <TextBlock
                Canvas.Top="192" Text="CP 4B1" Canvas.Left="480" FontSize="10"/>
            <TextBlock
                Canvas.Top="192" Text="CP 4B2" Canvas.Left="710" FontSize="10"/>
            <!-- Banco 6 -->
            <TextBlock
                Canvas.Top="259" Text="CP 3B1" Canvas.Left="480" FontSize="10"/>
            <TextBlock
                Canvas.Top="259" Text="CP 3B2" Canvas.Left="710" FontSize="10"/>
            <!-- Banco 7 -->
            <TextBlock
                Canvas.Top="395" Text="CP 2B1" Canvas.Left="480" FontSize="10"/>
            <TextBlock
                Canvas.Top="395" Text="CP 2B2" Canvas.Left="710" FontSize="10"/>
            <!-- Banco 8 -->
            <TextBlock
                Canvas.Top="461" Text="CP 1B1" Canvas.Left="480" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CP 1B2" Canvas.Left="710" FontSize="10"/>
            <!-- Banco 9 -->
            <TextBlock
                Canvas.Top="192" Text="CP 4C1" Canvas.Left="940" FontSize="10"/>
            <TextBlock
                Canvas.Top="192" Text="CP 4C2" Canvas.Left="1175" FontSize="10"/>
            <!-- Banco 10 -->
            <TextBlock
                Canvas.Top="259" Text="CP 3C1" Canvas.Left="940" FontSize="10"/>
            <TextBlock
                Canvas.Top="259" Text="CP 3C2" Canvas.Left="1175" FontSize="10"/>
            <!-- Banco 11 -->
            <TextBlock
                Canvas.Top="395" Text="CP 2C1" Canvas.Left="940" FontSize="10"/>
            <TextBlock
                Canvas.Top="395" Text="CP 2C2" Canvas.Left="1175" FontSize="10"/>
            <!-- Banco 12 -->
            <TextBlock
                Canvas.Top="461" Text="CP 1C1" Canvas.Left="940" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CP 1C2" Canvas.Left="1175" FontSize="10"/>
            <TextBlock
                Canvas.Top="325" Text="CS 4A2" Canvas.Left="245" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CS 3A1" Canvas.Left="-39" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CS 3A2" Canvas.Left="195" FontSize="10"/>
            <Image
                Width="100" VerticalAlignment="Center" Canvas.Top="142" Source="/Img/switch_open.png" x:Name="CS2A1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="102" Height="100" HorizontalAlignment="Left"/>
            <TextBlock
                Canvas.Top="192" Text="CS 2A1" Canvas.Left="80" FontSize="10"/>
            <TextBlock
                Canvas.Top="192" Text="CS 2A2" Canvas.Left="310" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CS 1A1" Canvas.Left="80" FontSize="10"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS1A2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="299" Height="100"/>
            <TextBlock
                Canvas.Top="461" Text="CS 1A2" Canvas.Left="310" FontSize="10"/>
            <TextBlock
                Canvas.Top="325" Text="CS 4B1" Canvas.Left="480" FontSize="10"/>
            <TextBlock
                Canvas.Top="325" Text="CS 4B2" Canvas.Left="710" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CS 3B1" Canvas.Left="425" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CS 3B2" Canvas.Left="655" FontSize="10"/>
            <TextBlock
                Canvas.Top="192" Text="CS 2B1" Canvas.Left="545" FontSize="10"/>
            <TextBlock
                Canvas.Top="192" Text="CS 2B2" Canvas.Left="775" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CS 1B1" Canvas.Left="545" FontSize="10"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS1B2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="764" Height="100"/>
            <TextBlock
                Canvas.Top="461" Text="CS 1B2" Canvas.Left="775" FontSize="10"/>
            <TextBlock
                Canvas.Top="325" Text="CS 4C1" Canvas.Left="940" FontSize="10"/>
            <TextBlock
                Canvas.Top="325" Text="CS 4C2" Canvas.Left="1175" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CS 3C1" Canvas.Left="885" FontSize="10"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS3C2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="1112" Height="100"/>
            <TextBlock
                Canvas.Top="192" Text="CS 2C1" Canvas.Left="1005" FontSize="10"/>
            <TextBlock
                Canvas.Top="192" Text="CS 2C2" Canvas.Left="1238" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CS 1C1" Canvas.Left="1005" FontSize="10"/>
            <TextBlock
                Canvas.Top="461" Text="CS 1C2" Canvas.Left="1238" FontSize="10"/>
            <!-- Chaves do Delta -->
            <TextBlock
                Canvas.Top="590" Text="CS 7A" Canvas.Left="220" FontSize="10"/>
            <TextBlock
                Canvas.Top="590" Text="CS 7B" Canvas.Left="670" FontSize="10"/>
            <TextBlock
                VerticalAlignment="Center" Canvas.Top="590" Text="CS 7C" Canvas.Left="1122" HorizontalAlignment="Left" FontSize="10"/>
            <!-- Chaves da Estrela -->
            <TextBlock
                Canvas.Top="665" Text="CS 6A" Canvas.Left="360" FontSize="10"/>
            <Image
                Width="100" VerticalAlignment="Top" Canvas.Top="634" Source="/Img/switch_open.png" x:Name="CS6B" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="790" Height="100" HorizontalAlignment="Center">
                <Image.RenderTransform
>
                    <RotateTransform
                        Angle="90" CenterY="25" CenterX="25"/>
                </Image.RenderTransform>
            </Image>
            <TextBlock
                Canvas.Top="665" Text="CS 6B" Canvas.Left="810" FontSize="10"/>
            <!-- Conexões Elétricas -->
            <Line
                Y1="293" Y2="293" X1="60" X2="-0" Width="58" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CS_4A1" Canvas.Left="2" Height="294" HorizontalAlignment="Left"/>
            <Line
                Y1="293" Y2="430" X1="2" X2="2" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CS_3A1" Canvas.Left="-1" HorizontalAlignment="Left"/>
            <Line
                Y1="293" Y2="430" X1="10" X2="10" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CS_3A2" Canvas.Left="224" HorizontalAlignment="Left"/>
            <Line
                Y1="158" Y2="158" X1="350" X2="58" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_BARRA_ENTRADA_CS_2A1_E_CS_2A2"/>
            <Line
                Y1="293" Y2="293" X1="233" X2="295" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CS_4A2"/>
            <Line
                Y1="293" Y2="430" X1="234" X2="234" Width="233" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CS_3A3" HorizontalAlignment="Left"/>
            <Line
                Y1="360" Y2="227" X1="118" X2="118" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_SAIDA_CS_2A1" Canvas.Left="2" HorizontalAlignment="Left"/>
            <Line
                Y1="360" Y2="227" X1="350" X2="350" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_SAIDA_CS_2A2"/>
            <Line
                Y1="495" Y2="577" X1="59" X2="59" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_SAIDA_CP_1A1" Canvas.Left="2" HorizontalAlignment="Left"/>
            <Line
                Y1="575" Y2="158" X1="409" X2="409" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_BARRA_SAIDA_CS_7A" HorizontalAlignment="Left"/>
            <Line
                Y1="576" Y2="576" X1="270" X2="409" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_SAIDA_CS_7A"/>
            <Line
                Y1="360" Y2="360" X1="60" X2="120" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_SAIDA_CS_4A1"/>
            <Line
                Y1="427" Y2="427" X1="60" X2="120" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CS_1A1"/>
            <Line
                Y1="427" Y2="427" X1="290" X2="350" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CS_1A2"/>
            <Line
                Y1="576" Y2="576" X1="59" X2="200" Width="198" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CS_7A" Canvas.Left="2" Height="577" HorizontalAlignment="Left"/>
            <Line
                Y1="495" Y2="650" X1="0" X2="0" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_SAIDA_CS_3A1" Canvas.Left="2" HorizontalAlignment="Left"/>
            <Line
                Y1="650" Y2="650" X1="0" X2="335" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CS_6A"/>
            <Line
                Y1="650" Y2="650" X1="410" X2="790" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_ENTRADA_CS_6B" Canvas.Left="-1" HorizontalAlignment="Left"/>
            <Line
                Y1="497" Y2="497" X1="0" X2="350" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_BARRA_SAIDA_CS_3A1_ATE_CS_1A2"/>
            <Line
                Y1="158" Y2="158" X1="815" X2="409" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_BARRA_ENTRADA_CS_2B1_ATE_CS_2B2"/>
            <Line
                Y1="293" Y2="293" X1="523" X2="465" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_ENTRADA_CS_4B1"/>
            <Line
                Y1="294" Y2="427" X1="466" X2="466" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_ENTRADA_CS_3B1"/>
            <Line
                Y1="292" Y2="292" X1="755" X2="697" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_ENTRADA_CS_4B2_"/>
            <Line
                Y1="292" Y2="430" X1="698" X2="698" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_ENTRADA_CS_3B2"/>
            <Line
                Y1="497" Y2="497" X1="465" X2="813" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_BARRA_SAIDA_CS_3B1_A_CS_1B2"/>
            <Line
                Y1="497" Y2="577" X1="524" X2="524" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_SAIDA_CP_1B1" Canvas.Left="2" HorizontalAlignment="Left"/>
            <Line
                Y1="575" Y2="158" X1="870" X2="870" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_BARRA_SAIDA_CS_7B"/>
            <Line
                Y1="576" Y2="576" X1="524" X2="650" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_ENTRADA_CS_7B"/>
            <Line
                Y1="576" Y2="576" X1="720" X2="870" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_SAIDA_7B"/>
            <Line
                Y1="360" Y2="360" X1="525" X2="583" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_SAIDA_CS_4B1"/>
            <Line
                Y1="360" Y2="360" X1="290" X2="350" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_SAIDA_CS_4A2"/>
            <Line
                Y1="360" Y2="227" X1="583" X2="583" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_SAIDA_CS_2B1"/>
            <Line
                Y1="427" Y2="427" X1="525" X2="583" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_ENTRADA_CS_1B1"/>
            <Line
                Y1="427" Y2="427" X1="757" X2="815" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_ENTRADA_CS_1B2"/>
            <Line
                Y1="427" Y2="427" X1="1225" X2="1279" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_ENTRADA_CS_1C2"/>
            <Line
                Y1="427" Y2="427" X1="986" X2="1047" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_ENTRADA_CS_1C1"/>
            <Line
                Y1="497" Y2="650" X1="465" X2="465" VerticalAlignment="Center" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_SAIDA_CS_3B1" Canvas.Left="2" HorizontalAlignment="Left"/>
            <Line
                Y1="292" Y2="292" X1="930" X2="990" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_ENTRADA_CS_4C1"/>
            <Line
                Y1="158" Y2="158" X1="1280" X2="870" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_BARRA_ENTRADA_CS_2C1_E_CS_2C2"/>
            <Line
                Y1="292" Y2="430" X1="931" X2="931" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_ENTRADA_CS_3C1"/>
            <Line
                Y1="360" Y2="360" X1="986" X2="1047" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_SAIDA_CS_4C1"/>
            <Line
                Y1="360" Y2="360" X1="757" X2="815" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_SAIDA_CS_4B2"/>
            <Line
                Y1="230" Y2="360" X1="1279" X2="1279" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_SAIDA_CS_2C2"/>
            <Line
                Y1="230" Y2="360" X1="1047" X2="1047" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_SAIDA_2C1"/>
            <Line
                Y1="292" Y2="292" X1="1220" X2="1162" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_ENTRADA_CS_4C2"/>
            <Line
                Y1="292" Y2="426" X1="1163" X2="1163" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_ENTRADA_CS_3C2"/>
            <Line
                Y1="230" Y2="360" X1="815" X2="815" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_SAIDA_CS_2B2"/>
            <Line
                Y1="360" Y2="360" X1="1223" X2="1279" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_SAIDA_CS_4C2"/>
            <Line
                Y1="125" Y2="576" X1="1320" X2="1320" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_BARRA_SAIDA_CS_7C"/>
            <Line
                Y1="497" Y2="497" X1="933" X2="1280" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_BARRA_SAIDA_CS_3C1_A_CS_1C2"/>
            <Line
                Y1="125" Y2="160" X1="291" X2="291" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_ENTRADA_CP_4A2"/>
            <Line
                Y1="125" Y2="125" X1="291" X2="1320" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_BARRA_SAIDA_CS_7C_ATE_CP_4A2"/>
            <Line
                Y1="576" Y2="576" X1="989" X2="1110" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_ENTRADA_CS_7C"/>
            <Line
                Y1="497" Y2="577" X1="988" X2="988" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_SAIDA_CP_1C1"/>
            <Line
                Y1="576" Y2="576" X1="1180" X2="1320" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_SAIDA_CS_7C"/>
            <Line
                Y1="497" Y2="650" X1="930" X2="930" VerticalAlignment="Top" Canvas.Top="2" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_SAIDA_CS_3C1" Canvas.Left="2" HorizontalAlignment="Center"/>
            <Line
                Y1="35" Y2="650" X1="1325" X2="1325" Width="1339" VerticalAlignment="Center" Canvas.Top="2" StrokeThickness="2" Stroke="Black" x:Name="BARRA_DE_NEUTRO__ENTRADA_CS_C6" Canvas.Left="5" Height="709" HorizontalAlignment="Left"/>
            <Line
                Y1="650" Y2="650" X1="850" X2="1325" Width="1328" VerticalAlignment="Center" Canvas.Top="2" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_SAIDA_CS_6B" Canvas.Left="6" Height="652" HorizontalAlignment="Left"/>
            <!-- Indicador da potência total -->
            <TextBlock
                Canvas.Top="-76" Text="Total Power: 0 MVAr" x:Name="TotalPowerLabel" Canvas.Left="529" FontSize="20" Background="SkyBlue"/>
            <!-- DockPanel para posicionar a imagem -->
            <DockPanel
                HorizontalAlignment="Left" VerticalAlignment="Bottom"/>
            <Line
                Y1="360" Y2="227" X1="350" X2="350" Width="351" VerticalAlignment="Center" Canvas.Top="-125" StrokeThickness="2" Stroke="Black" x:Name="FASE_B_SAIDA_TC_2" Canvas.Left="287" Height="284" HorizontalAlignment="Center"/>
            <Line
                Y1="360" Y2="227" X1="350" X2="350" Width="352" VerticalAlignment="Center" Canvas.Top="-125" StrokeThickness="2" Stroke="Black" x:Name="FASE_C_SAIDA_TC_3" Canvas.Left="756" Height="283" HorizontalAlignment="Left"/>
            <Line
                Y1="360" Y2="227" X1="350" X2="350" Width="352" VerticalAlignment="Center" Canvas.Top="-126" StrokeThickness="2" Stroke="Black" x:Name="FASE_A_SAIDA_TC_1" Canvas.Left="-174" Height="283" HorizontalAlignment="Left"/>
            <!-- Transformadores de Corrente -->
            <Image
                Width="100" VerticalAlignment="Center" Canvas.Top="18" Source="/Img/tc.png" Canvas.Left="160" Height="100" HorizontalAlignment="Left"/>
            <TextBlock
                Canvas.Top="60" Text="TC-1" RenderTransformOrigin="2.547,-3.534" x:Name="CTAValue" Canvas.Left="124" FontSize="10"/>
            <Image
                Width="100" VerticalAlignment="Center" Canvas.Top="18" Source="/Img/tc.png" Canvas.Left="621" Height="100" HorizontalAlignment="Left"/>
            <TextBlock
                Canvas.Top="60" Text="TC-2" x:Name="CTBValue" Canvas.Left="588" FontSize="10"/>
            <Image
                Width="100" VerticalAlignment="Top" Canvas.Top="17" Source="/Img/tc.png" Canvas.Left="1056" Height="100" HorizontalAlignment="Center"/>
            <TextBlock
                VerticalAlignment="Center" Canvas.Top="60" Text="TC-3" x:Name="CTCValue" Canvas.Left="1064" HorizontalAlignment="Center" FontSize="10"/>
            <!-- Chaves de Entrada -->
            <Image
                Width="100" Canvas.Top="-51" Source="/Img/switch_open.png" x:Name="CSA" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="125" Height="100"/>
            <TextBlock
                VerticalAlignment="Top" Canvas.Top="-22" Text="CS A" Canvas.Left="127" HorizontalAlignment="Left" FontSize="10"/>
            <Image
                Width="100" VerticalAlignment="Center" Canvas.Top="-51" Source="/Img/switch_open.png" x:Name="CSB" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="620" Height="100" HorizontalAlignment="Left"/>
            <TextBlock
                VerticalAlignment="Top" Canvas.Top="-18" Text="CS B" Canvas.Left="590" HorizontalAlignment="Left" FontSize="10"/>
            <Image
                Width="100" VerticalAlignment="Top" Canvas.Top="-53" Source="/Img/switch_open.png" x:Name="CSC" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="1055" Height="100" HorizontalAlignment="Center"/>
            <TextBlock
                VerticalAlignment="Center" Canvas.Top="-18" Text="CS C" Canvas.Left="1059" HorizontalAlignment="Left" FontSize="10"/>
            <!-- Chaves da Neutro -->
            <Image
                Width="100" VerticalAlignment="Center" Canvas.Top="-51" Source="/Img/switch_open.png" x:Name="CS6C" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="1264" Height="100" HorizontalAlignment="Left">
                <Image.RenderTransform
>
                    <TransformGroup
>
                        <ScaleTransform
/>
                        <SkewTransform
/>
                        <RotateTransform
                            Angle="0.479"/>
                        <TranslateTransform
                            X="50" Y="-3.5527136788005009E-15"/>
                    </TransformGroup>
                </Image.RenderTransform>
            </Image>
            <TextBlock
                Canvas.Top="-16" Text="CS 6C [Neutro]" Canvas.Left="1238" FontSize="10"/>
            <Canvas
                VerticalAlignment="Center" Canvas.Top="-2" Canvas.Left="301" HorizontalAlignment="Center"/>
            <Image
                Width="100" Canvas.Top="142" Source="/Img/Cap.png" x:Name="CP4C2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="1171" Height="100"/>
            <Image
                Width="100" Canvas.Top="209" Source="/Img/Cap.png" x:Name="CP3C1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="938" Height="100"/>
            <Image
                Width="100" Canvas.Top="209" Source="/Img/Cap.png" x:Name="CP3C2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="1171" Height="100"/>
            <Image
                Width="100" Canvas.Top="345" Source="/Img/Cap.png" x:Name="CP2C1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="938" Height="100"/>
            <Image
                Width="100" Canvas.Top="345" Source="/Img/Cap.png" x:Name="CP2C2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="1171" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/Cap.png" x:Name="CP1C1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="938" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/Cap.png" x:Name="CP1C2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="1171" Height="100"/>
            <Image
                Width="100" Canvas.Top="209" Source="/Img/Cap.png" x:Name="CP3A1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="9" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/Cap.png" x:Name="CP1A1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="9" Height="100"/>
            <Image
                Width="100" Canvas.Top="345" Source="/Img/Cap.png" x:Name="CP2A1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="9" Height="100"/>
            <Image
                Width="100" Canvas.Top="142" Source="/Img/Cap.png" x:Name="CP4C1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="938" Height="100"/>
            <Image
                Width="100" Canvas.Top="142" Source="/Img/Cap.png" x:Name="CP4B1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="474" Height="100"/>
            <Image
                Width="100" Canvas.Top="142" Source="/Img/Cap.png" x:Name="CP4B2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="706" Height="100"/>
            <Image
                Width="100" Canvas.Top="209" Source="/Img/Cap.png" x:Name="CP3B1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="474" Height="100"/>
            <Image
                Width="100" Canvas.Top="209" Source="/Img/Cap.png" x:Name="CP3B2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="706" Height="100"/>
            <Image
                Width="100" Canvas.Top="345" Source="/Img/Cap.png" x:Name="CP2B1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="474" Height="100"/>
            <Image
                Width="100" Canvas.Top="345" Source="/Img/Cap.png" x:Name="CP2B2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="706" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/Cap.png" x:Name="CP1B1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="474" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/Cap.png" x:Name="CP1B2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="706" Height="100"/>
            <Image
                Width="100" Canvas.Top="142" Source="/Img/Cap.png" x:Name="CP4A2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="241" Height="100"/>
            <Image
                Width="100" Canvas.Top="209" Source="/Img/Cap.png" x:Name="CP3A2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="241" Height="100"/>
            <Image
                Width="100" Canvas.Top="345" Source="/Img/Cap.png" x:Name="CP2A2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="241" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/Cap.png" x:Name="CP1A2" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="241" Height="100"/>
            <TextBlock
                Canvas.Top="259" Text="CP 3A1" Canvas.Left="15" FontSize="10"/>
            <Image
                Width="100" Canvas.Top="275" Source="/Img/switch_open.png" x:Name="CS4A1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="9" Height="100"/>
            <TextBlock
                Canvas.Top="325" Text="CS 4A1" Canvas.Left="15" FontSize="10"/>
            <Image
                Width="100" VerticalAlignment="Center" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS3A1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="-16" Height="100" HorizontalAlignment="Left"/>
            <Image
                Width="100" VerticalAlignment="Center" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS1A1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="103" Height="100" HorizontalAlignment="Left"/>
            <Image
                Width="100" Canvas.Top="275" Source="/Img/switch_open.png" x:Name="CS4C1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="938" Height="100"/>
            <Image
                Width="100" Canvas.Top="275" Source="/Img/switch_open.png" x:Name="CS4C2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="1171" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS3C1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="880" Height="100"/>
            <TextBlock
                Canvas.Top="461" Text="CS 3C2" Canvas.Left="1120" FontSize="10"/>
            <Image
                Width="100" Canvas.Top="142" Source="/Img/switch_open.png" x:Name="CS2C1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="996" Height="100"/>
            <Image
                Width="100" Canvas.Top="142" Source="/Img/switch_open.png" x:Name="CS2C2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="1228" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS1C1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="996" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS1C2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="1228" Height="100"/>
            <Image
                Width="100" VerticalAlignment="Center" Canvas.Top="560" Source="/Img/switch_open.png" x:Name="CS7C" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="1144" Height="100" HorizontalAlignment="Left">
                <Image.RenderTransform
>
                    <TransformGroup
>
                        <ScaleTransform
/>
                        <SkewTransform
/>
                        <RotateTransform
                            Angle="89.61"/>
                        <TranslateTransform
                            X="50" Y="-3.5527136788005009E-15"/>
                    </TransformGroup>
                </Image.RenderTransform>
            </Image>
            <Image
                Width="100" Canvas.Top="275" Source="/Img/switch_open.png" x:Name="CS4B1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="474" Height="100"/>
            <Image
                Width="100" Canvas.Top="275" Source="/Img/switch_open.png" x:Name="CS4B2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="705" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS3B1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="415" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS3B2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="647" Height="100"/>
            <Image
                Width="100" Canvas.Top="142" Source="/Img/switch_open.png" x:Name="CS2B1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="532" Height="100"/>
            <Image
                Width="100" Canvas.Top="142" Source="/Img/switch_open.png" x:Name="CS2B2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="764" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS1B1" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="532" Height="100"/>
            <Image
                Width="100" Canvas.Top="560" Source="/Img/switch_open.png" x:Name="CS7A" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="200" Height="100">
                <Image.RenderTransform
>
                    <RotateTransform
                        Angle="90" CenterY="25" CenterX="25"/>
                </Image.RenderTransform>
            </Image>
            <Image
                Width="100" Canvas.Top="560" Source="/Img/switch_open.png" x:Name="CS7B" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="650" Height="100">
                <Image.RenderTransform
>
                    <RotateTransform
                        Angle="90" CenterY="25" CenterX="25"/>
                </Image.RenderTransform>
            </Image>
            <Image
                Width="100" VerticalAlignment="Top" Canvas.Top="634" Source="/Img/switch_open.png" x:Name="CS6A" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="340" Height="100" HorizontalAlignment="Center">
                <Image.RenderTransform
>
                    <RotateTransform
                        Angle="90" CenterY="25" CenterX="25"/>
                </Image.RenderTransform>
            </Image>
            <Image
                Width="100" Canvas.Top="275" Source="/Img/switch_open.png" x:Name="CS4A2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="241" Height="100"/>
            <Image
                Width="100" Canvas.Top="411" Source="/Img/switch_open.png" x:Name="CS3A2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="183" Height="100"/>
            <Image
                Width="100" VerticalAlignment="Center" Canvas.Top="142" Source="/Img/switch_open.png" x:Name="CS2A2" MouseLeftButtonDown="SwitchControl_MouseLeftButtonDown" Canvas.Left="334" Height="100" HorizontalAlignment="Left"/>
            <Image
                Width="100" Canvas.Top="143" Source="/Img/Cap.png" x:Name="CP4A1" MouseLeftButtonDown="Capacitor_MouseLeftButtonDown" Canvas.Left="44" Height="100" HorizontalAlignment="Left" VerticalAlignment="Top"/>
        </Canvas>
        <Image x:Name="Data_Logo" Source="/Img/logo.png" RenderTransformOrigin="0.597,0.505" Margin="10,11,120,978"/>
        <!-- Visualization Panel -->
    </Grid>

</Window>