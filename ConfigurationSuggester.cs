﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace CapBankAutomation
{
    public class ConfigurationSuggester
    {
        public readonly Dictionary<string, List<string>> switchesByVoltage;
        public readonly Dictionary<string, List<string>> capacitorsByVoltage;
        protected readonly Dictionary<string, double[]> qSwitchPowers;
        
        public string CircuitType { get; set; }

        public ConfigurationSuggester()
        {
            qSwitchPowers = new Dictionary<string, double[]>
            {
                {"CP1A1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP1A2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP1B1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP1B2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP1C1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP1C2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP2A1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP2A2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP2B1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP2B2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP2C1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP2C2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP3A1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP3A2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP3B1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP3B2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP3C1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP3C2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP4A1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP4A2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP4B1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP4B2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}},
                {"CP4C1", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}, {"CP4C2", new double[] {0.1, 0.2, 0.8, 1.2, 1.6}}
            };

            switchesByVoltage = new Dictionary<string, List<string>>
            {
                {"13.8", new List<string> {"CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS2A1", "CS2A2", "CS1B1", "CS1B2", "CS2B1", "CS2B2", "CS1C1", "CS1C2", "CS2C1", "CS2C2", "CS7A", "CS7B", "CS7C"}},
                {"23.9", new List<string> {"CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS2A1", "CS2A2", "CS1B1", "CS1B2", "CS2B1", "CS2B2", "CS1C1", "CS1C2", "CS2C1", "CS2C2", "CS6A", "CS6B"}},
                {"27.6", new List<string> {"CSA", "CSB", "CSC", "CS2A1", "CS2A2", "CS2B1", "CS2B2", "CS2C1", "CS2C2", "CS3A1", "CS3A2", "CS3B1", "CS3B2", "CS3C1", "CS3C2", "CS7A", "CS7B", "CS7C"}},
                {"41.4", new List<string> {"CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS4A1", "CS4A2", "CS1B1", "CS1B2", "CS4B1", "CS4B2", "CS1C1", "CS1C2", "CS4C1", "CS4C2", "CS7A", "CS7B", "CS7C"}},
                {"47.8", new List<string> {"CSA", "CSB", "CSC", "CS2A1", "CS2A2", "CS2B1", "CS2B2", "CS2C1", "CS2C2", "CS3A1", "CS3A2", "CS3B1", "CS3B2", "CS3C1", "CS3C2", "CS6A", "CS6B"}},
                {"55.2", new List<string> {"CSA", "CSB", "CSC", "CS4A1", "CS4A2", "CS4B1", "CS4B2", "CS4C1", "CS4C2", "CS7A", "CS7B", "CS7C"}},
                {"71.7", new List<string> {"CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS4A1", "CS4A2", "CS1B1", "CS1B2", "CS4B1", "CS4B2", "CS1C1", "CS1C2", "CS4C1", "CS4C2", "CS6A", "CS6B"}},
                {"95.6", new List<string> {"CSA", "CSB", "CSC", "CS4A1", "CS4A2", "CS4B1", "CS4B2", "CS4C1", "CS4C2", "CS6A", "CS6B"}}
            };

            capacitorsByVoltage = new Dictionary<string, List<string>>
            {
                {"13.8", new List<string> {"CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2"}},
                {"23.9", new List<string> {"CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2"}},
                {"27.6", new List<string> {"CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"}},
                {"41.4", new List<string> {"CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"}},
                {"47.8", new List<string> {"CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"}},
                {"55.2", new List<string> {"CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"}},
                {"71.7", new List<string> {"CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"}},
                {"95.6", new List<string> {"CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"}}
            };
        }

        public virtual (bool Success, string CsConfiguration, string QConfigurationBest, List<string> CapacitorsBest, string QConfigurationBelow, List<string> CapacitorsBelow, string QConfigurationAbove, List<string> CapacitorsAbove, double ClosestPowerBelow, double ClosestPowerAbove, double BestOptionPower, double MaxPowerGroup1) SuggestConfiguration(string voltage, double desiredPower, string circuitType)
        {
            this.CircuitType = circuitType;
            var emptyResult = (false, "", "", new List<string>(), "", new List<string>(), "", new List<string>(), 0.0, 0.0, 0.0, 0.0);

            if (!capacitorsByVoltage.ContainsKey(voltage)) return emptyResult;

            var availableCapacitors = capacitorsByVoltage[voltage];
            var group1Capacitors = availableCapacitors.Where(c => c.EndsWith("1")).ToList();
            double maxPowerGroup1 = group1Capacitors.Sum(c => qSwitchPowers[c].Sum());

            var capacitorsToUse = desiredPower <= maxPowerGroup1 ? group1Capacitors : availableCapacitors;
            double maxPowerAvailable = capacitorsToUse.Sum(c => qSwitchPowers[c].Sum());

            if (desiredPower > maxPowerAvailable) return emptyResult;

            var qCombinations = GenerateQCombinations();
            var options = new List<(string Configuration, double Power, List<string> Capacitors)>();

            foreach (var combination in qCombinations)
            {
                double power = capacitorsToUse.Count * combination.Sum(q => qSwitchPowers.First().Value[q - 1]);
                options.Add((FormatQConfiguration(combination), power, capacitorsToUse));
            }

            options = options.OrderBy(o => Math.Abs(o.Power - desiredPower)).ToList();

            var bestOption = options.FirstOrDefault();
            var closestBelow = options.Where(o => o.Power < desiredPower).OrderByDescending(o => o.Power).FirstOrDefault();
            var closestAbove = options.Where(o => o.Power > desiredPower).OrderBy(o => o.Power).FirstOrDefault();
            
            string csConfiguration = GetCsConfiguration(voltage, desiredPower, maxPowerGroup1, bestOption.Capacitors);

            return (true, csConfiguration, bestOption.Configuration, bestOption.Capacitors, 
                    closestBelow.Configuration, closestBelow.Capacitors, 
                    closestAbove.Configuration, closestAbove.Capacitors, 
                    closestBelow.Power, closestAbove.Power, bestOption.Power, maxPowerGroup1);
        }

        protected List<List<int>> GenerateQCombinations()
        {
            var combinations = new List<List<int>>();
            for (int i = 1; i < (1 << 5); i++)
            {
                var combination = new List<int>();
                for (int j = 0; j < 5; j++)
                {
                    if ((i & (1 << j)) != 0)
                    {
                        combination.Add(j + 1);
                    }
                }
                combinations.Add(combination);
            }
            return combinations;
        }

        protected string GetCsConfiguration(string voltage, double desiredPower, double maxPowerGroup1, List<string> capacitorsUsed)
        {
            if (!switchesByVoltage.ContainsKey(voltage)) return "";

            var csConfig = new List<string>();
            var availableSwitches = switchesByVoltage[voltage];
            bool useGroup1Only = desiredPower <= maxPowerGroup1;
            
            var capacitorGroupsUsed = new HashSet<string>(capacitorsUsed.Select(c => c.Substring(2, 3))); // e.g., "1A1", "2C2"
            
            foreach (var switchName in availableSwitches)
            {
                // Regra geral para chaves principais
                if (switchName.Length == 3 && new[]{"CSA", "CSB", "CSC"}.Contains(switchName))
                {
                    csConfig.Add(switchName);
                    continue;
                }
                
                // Pula chaves do grupo 2 se não forem necessárias
                if (useGroup1Only && switchName.Length > 4 && switchName.EndsWith("2"))
                {
                    continue;
                }

                // Adiciona chaves que correspondem aos capacitores usados
                string switchGroup = switchName.Substring(2); // e.g., "1A1", "7A"
                if(capacitorGroupsUsed.Any(capGroup => capGroup.Contains(switchGroup.Substring(1,2)))) // e.g., "1A" in "1A1"
                {
                    csConfig.Add(switchName);
                }
                // Adicionar chaves de interligação como 6A, 7B etc.
                else if (char.IsDigit(switchName[2]) && switchName.Length > 3 && !char.IsDigit(switchName[4]))
                {
                     if(capacitorsUsed.Any(c => c.Contains(switchName[3])))
                        csConfig.Add(switchName);
                }
            }
            // Adiciona chaves de interligação específicas que podem ser necessárias
            if(csConfig.Any(s => s.Contains("A"))) csConfig.AddRange(availableSwitches.Where(sw => sw == "CS7A" || sw == "CS6A"));
            if(csConfig.Any(s => s.Contains("B"))) csConfig.AddRange(availableSwitches.Where(sw => sw == "CS7B" || sw == "CS6B"));
            if(csConfig.Any(s => s.Contains("C"))) csConfig.AddRange(availableSwitches.Where(sw => sw == "CS7C" || sw == "CS6C"));


            return string.Join(",", csConfig.Distinct());
        }
        
        protected string FormatQConfiguration(List<int> qCombination)
        {
            if (qCombination == null || qCombination.Count == 0) return "";
            return string.Join(",", qCombination.Select(q => $"Q{q}"));
        }
    }

    public class ConfigurationSuggesterMonophase : ConfigurationSuggester
    {
        public ConfigurationSuggesterMonophase() : base()
        {
            // Usa os mesmos dicionários da classe base.
        }

        public (bool Success, string CsConfiguration, string QConfigurationBest, List<string> CapacitorsBest, string QConfigurationBelow, List<string> CapacitorsBelow, string QConfigurationAbove, List<string> CapacitorsAbove, double ClosestPowerBelow, double ClosestPowerAbove, double BestOptionPower, double MaxPowerGroup1) SuggestConfiguration(string voltage, double desiredPower, Dictionary<string, List<(List<int> Combination, double Power)>> capacitorSwitchCombinations)
        {
            var emptyResult = (false, "", "", new List<string>(), "", new List<string>(), "", new List<string>(), 0.0, 0.0, 0.0, 0.0);
            if (!capacitorsByVoltage.ContainsKey(voltage)) return emptyResult;

            var availableCapacitors = capacitorsByVoltage[voltage];
            var group1Capacitors = availableCapacitors.Where(c => c.EndsWith("1")).ToList();
            double maxPowerGroup1 = group1Capacitors.Sum(c => qSwitchPowers[c].Sum());

            bool useGroup1Only = desiredPower <= maxPowerGroup1;
            var selectedCapacitors = useGroup1Only ? group1Capacitors : availableCapacitors;

            double maxPowerAvailable = selectedCapacitors.Sum(c => qSwitchPowers[c].Sum());
            if (desiredPower > maxPowerAvailable) return emptyResult;

            capacitorSwitchCombinations.Clear();
            var bestCombination = FindBestCombination(desiredPower, selectedCapacitors, new Dictionary<string, List<(List<int> Combination, double Power)>>());

            if (bestCombination.Power < 0.01) return emptyResult;

            foreach(var capConfig in bestCombination.Configuration)
            {
                capacitorSwitchCombinations.Add(capConfig.Key, capConfig.Value);
            }

            var capacitorsBest = bestCombination.Configuration.Keys.ToList();
            string csConfiguration = GetCsConfigurationMonophase(voltage, capacitorsBest);
            string qConfigurationBest = "Individual"; // Representação para UI

            return (true, csConfiguration, qConfigurationBest, capacitorsBest, null, null, null, null, 0, 0, bestCombination.Power, maxPowerGroup1);
        }

        private (Dictionary<string, List<(List<int> Combination, double Power)>> Configuration, double Power) FindBestCombination(double targetPower, List<string> remainingCaps, Dictionary<string, List<(List<int> Combination, double Power)>> currentConfig)
        {
            if (remainingCaps.Count == 0)
            {
                double totalPower = currentConfig.Values.Sum(configs => configs.Sum(c => c.Power));
                return (currentConfig, totalPower);
            }

            var bestResult = (Configuration: currentConfig, Power: currentConfig.Values.Sum(configs => configs.Sum(c => c.Power)));

            // Opção 1: Não incluir o capacitor atual
            var resultWithout = FindBestCombination(targetPower, remainingCaps.Skip(1).ToList(), currentConfig);
            if (Math.Abs(targetPower - resultWithout.Power) < Math.Abs(targetPower - bestResult.Power))
            {
                bestResult = resultWithout;
            }

            // Opção 2: Incluir o capacitor atual com a melhor combinação de chaves
            string currentCap = remainingCaps.First();
            var remainingCapsForNext = remainingCaps.Skip(1).ToList();
            var allQCombinations = GenerateQCombinations().OrderByDescending(c => CalculatePowerForCombination(currentCap, c)).ToList();

            foreach (var comb in allQCombinations)
            {
                double combPower = CalculatePowerForCombination(currentCap, comb);
                if (combPower > 0)
                {
                    var newConfig = new Dictionary<string, List<(List<int> Combination, double Power)>>(currentConfig);
                    newConfig[currentCap] = new List<(List<int> Combination, double Power)> { (comb, combPower) };
                    
                    var resultWith = FindBestCombination(targetPower, remainingCapsForNext, newConfig);

                    if (Math.Abs(targetPower - resultWith.Power) < Math.Abs(targetPower - bestResult.Power))
                    {
                        bestResult = resultWith;
                    }
                }
            }

            return bestResult;
        }

        private double CalculatePowerForCombination(string capacitor, List<int> combination)
        {
            if (!qSwitchPowers.ContainsKey(capacitor)) return 0;
            return combination.Sum(qIndex => qSwitchPowers[capacitor][qIndex - 1]);
        }

        private string GetCsConfigurationMonophase(string voltage, List<string> capacitorsUsed)
        {
            if (!switchesByVoltage.ContainsKey(voltage) || capacitorsUsed == null) return "";

            var csConfig = new List<string>();
            var availableSwitches = switchesByVoltage[voltage];
            var phasesUsed = new HashSet<char>(capacitorsUsed.Select(c => c[3])); // A, B, C

            // Adiciona chaves de fase principal se a fase estiver em uso
            if (phasesUsed.Contains('A')) csConfig.Add("CSA");
            if (phasesUsed.Contains('B')) csConfig.Add("CSB");
            if (phasesUsed.Contains('C')) csConfig.Add("CSC");
            
            // Adiciona chaves de capacitores específicos
            foreach (var cap in capacitorsUsed)
            {
                string capId = cap.Substring(2); // e.g., 1A1, 2B2
                csConfig.Add($"CS{capId}");
            }

            // Adiciona chaves de interligação se necessário (ex: monofásico para neutro)
            csConfig.Add("CS6A");
            csConfig.Add("CS6B");
            csConfig.Add("CS6C");

            return string.Join(",", csConfig.Distinct());
        }
    }
}