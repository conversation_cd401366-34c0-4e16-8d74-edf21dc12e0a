﻿<Window x:Class="CapBankAutomation.Cap_Individual"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Capacitor Details" Height="300" Width="750"
        MaxHeight="300" MaxWidth="750">
    <Canvas Margin="0,0,5,9">
        <!-- Q1 -->
        <Image x:Name="Q1" Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\switch_open.png" Canvas.Left="51" Canvas.Top="24" Width="50" Height="100" HorizontalAlignment="Center" VerticalAlignment="Center" MouseLeftButtonDown="Switch_MouseLeftButtonDown" />
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="51" Canvas.Top="94" Width="50" Height="100" />

        <!-- Q2 -->
        <Image x:Name="Q2" Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\switch_open.png" Canvas.Left="149" Canvas.Top="24" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Top" MouseLeftButtonDown="Switch_MouseLeftButtonDown" />
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="150" Canvas.Top="93" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" />

        <!-- Q3 -->
        <Image x:Name="Q3" Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\switch_open.png" Canvas.Left="233" Canvas.Top="24" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" MouseLeftButtonDown="Switch_MouseLeftButtonDown" />
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="233" Canvas.Top="94" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" />

        <!-- Q4 -->
        <Image x:Name="Q4" Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\switch_open.png" Canvas.Left="360" Canvas.Top="24" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" MouseLeftButtonDown="Switch_MouseLeftButtonDown" />
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="360" Canvas.Top="93" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" />

        <!-- Q5 -->
        <Image x:Name="Q5" Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\switch_open.png" Canvas.Left="534" Canvas.Top="24" Width="50" Height="100" RenderTransformOrigin="-1.28,0.95" MouseLeftButtonDown="Switch_MouseLeftButtonDown" />
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="544" Canvas.Top="93" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" />
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="590" Canvas.Top="94" Width="50" Height="100" RenderTransformOrigin="0.58,0.55" HorizontalAlignment="Left" VerticalAlignment="Center" />
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="635" Canvas.Top="94" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" />

        <!-- Horizontal Lines -->
        <Line X1="25" Y1="40" X2="725" Y2="40" Stroke="Black" StrokeThickness="2" />
        <Line X1="25" Y1="178" X2="725" Y2="178" Stroke="Black" StrokeThickness="2" Height="382" Width="795" />
        <Line X1="250" Y1="110" X2="290" Y2="110" Stroke="Black" StrokeThickness="2" />
        <Line X1="375" Y1="110" X2="465" Y2="110" Stroke="Black" StrokeThickness="2" />
        <Line X1="560" Y1="110" X2="695" Y2="110" Stroke="Black" StrokeThickness="2" />

        <!-- Labels -->
        <TextBlock Canvas.Left="44" Canvas.Top="55" Text="Q1" RenderTransformOrigin="-1.933,4.639" HorizontalAlignment="Left" VerticalAlignment="Center" />
        <TextBlock Canvas.Left="129" Canvas.Top="55" Text="Q2" RenderTransformOrigin="-2.9,-3.444" />
        <TextBlock Canvas.Left="210" Canvas.Top="55" Text="Q3" />
        <TextBlock Canvas.Left="344" Canvas.Top="55" Text="Q4" HorizontalAlignment="Left" VerticalAlignment="Center" />
        <TextBlock Canvas.Left="526" Canvas.Top="55" Text="Q5" HorizontalAlignment="Left" VerticalAlignment="Center" />

        <TextBlock Canvas.Left="46" Canvas.Top="135" Text="C1" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="136" Canvas.Top="135" Text="C2" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="216" Canvas.Top="135" Text="C3" RenderTransformOrigin="0.719,0.566" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="347" Canvas.Top="135" Text="C3" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="531" Canvas.Top="135" Text="C3" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="578" Canvas.Top="135" Text="C3" RenderTransformOrigin="0.719,0.566" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="619" Canvas.Top="135" Text="C3" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="396" Canvas.Top="135" Text="C3" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="265" Canvas.Top="1365" Text="C3" />
        <TextBlock Canvas.Left="36" Canvas.Top="195" HorizontalAlignment="Left" VerticalAlignment="Top" RenderTransformOrigin="0.49,3.135" ><Run Text="C1"/><Run Text="=&gt;"/><Run Text="100K"/><Run Language="pt-br" Text="VA"/><Run Text="r"/></TextBlock>
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="406" Canvas.Top="93" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" />
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="274" Canvas.Top="93" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" />
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="678" Canvas.Top="94" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" />
        <TextBlock Canvas.Left="666" Canvas.Top="135" Text="C3" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="136" Canvas.Top="195" RenderTransformOrigin="0.49,3.135" HorizontalAlignment="Left" VerticalAlignment="Top" ><Run Text="C"/><Run Text="2"/><Run Text="=&gt;"/><Run Text="2"/><Run Text="00"/><Run Language="pt-br" Text="KVA"/><Run Text="r"/></TextBlock>
        <TextBlock Canvas.Left="396" Canvas.Top="195" RenderTransformOrigin="0.49,3.135" HorizontalAlignment="Left" VerticalAlignment="Top" ><Run Text="C"/><Run Text="3"/><Run Text="=&gt;"/><Run Text="4"/><Run Text="00K"/><Run Language="pt-br" Text="VA"/><Run Text="r"/></TextBlock>
        <Image Source="D:\C#\CapBankAutomation\CapBankAutomation Rev1.1\Img\Cap.png" Canvas.Left="452" Canvas.Top="94" Width="50" Height="100" HorizontalAlignment="Left" VerticalAlignment="Center" />
        <!-- Label para exibir o nome do capacitor -->
        <TextBlock x:Name="CapacitorNameTextBlock" Text="Capacitor: " Canvas.Left="300" Canvas.Top="10" FontSize="16" />

        <!-- Label para exibir a potência total -->
        <TextBlock x:Name="TotalPowerLabel" Text="Total Power: 0 MVAr" Canvas.Left="300" Canvas.Top="210" FontSize="16" />
        <TextBlock Canvas.Left="441" Canvas.Top="134" Text="C3" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <TextBlock Canvas.Left="264" Canvas.Top="135" Text="C3" RenderTransformOrigin="0.719,0.566" HorizontalAlignment="Center" VerticalAlignment="Top" />

    </Canvas>
</Window> 