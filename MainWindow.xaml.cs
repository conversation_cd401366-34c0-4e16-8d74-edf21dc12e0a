using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CapBankAutomation
{
    public class CapacitorState
    {
        public double Power { get; set; }
        public bool[] SwitchStates { get; set; }
        public bool IsModified { get; set; }
        public bool IsSelected { get; set; }

        public CapacitorState(double power, bool[] switchStates)
        {
            Power = power;
            SwitchStates = switchStates;
            IsModified = false;
            IsSelected = false;
        }
    }

    public partial class MainWindow : Window
    {
        private readonly ConfigurationSuggester _configSuggester;
        internal Dictionary<string, CapacitorState> _capacitorStates;
        private readonly CapacitorColorManager _colorManager;
        internal readonly BitmapImage _openImage;
        internal readonly BitmapImage _closedImage;
        internal (bool Success, string CsConfiguration, string QConfigurationBest, List<string> CapacitorsBest) _suggestedConfiguration;
        
        private readonly CheckThreePhase _checkThreePhase;
        private readonly CheckMonophase _checkMonoPhase;
        
        public Dictionary<string, bool[]> CapacitorConfigurations { get; private set; } = new Dictionary<string, bool[]>();
        private readonly Dictionary<string, bool> switchStates = new Dictionary<string, bool>();

        public MainWindow()
        {
            InitializeComponent();

            _openImage = new BitmapImage(new Uri("pack://application:,,,/Img/switch_open.png"));
            _closedImage = new BitmapImage(new Uri("pack://application:,,,/Img/switch_closed.png"));

            _configSuggester = new ConfigurationSuggester();
            _colorManager = new CapacitorColorManager();

            _checkThreePhase = new CheckThreePhase(this);
            _checkMonoPhase = new CheckMonophase(this);

            InitializeCapacitorStates();
            InitializeSwitchStates();

            RegisterEventHandlers();

            // Configuração inicial do ComboBox será feita no evento Loaded
            this.Loaded += MainWindow_Loaded;
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // Configuração inicial será feita aqui quando os elementos estiverem carregados
        }

        private void InitializeCapacitorStates()
        {
            _capacitorStates = new Dictionary<string, CapacitorState>
            {
                {"CP1A1", new CapacitorState(1.0, new bool[5])}, {"CP1A2", new CapacitorState(1.0, new bool[5])},
                {"CP1B1", new CapacitorState(1.0, new bool[5])}, {"CP1B2", new CapacitorState(1.0, new bool[5])},
                {"CP1C1", new CapacitorState(1.0, new bool[5])}, {"CP1C2", new CapacitorState(1.0, new bool[5])},
                {"CP2A1", new CapacitorState(2.0, new bool[5])}, {"CP2A2", new CapacitorState(2.0, new bool[5])},
                {"CP2B1", new CapacitorState(2.0, new bool[5])}, {"CP2B2", new CapacitorState(2.0, new bool[5])},
                {"CP2C1", new CapacitorState(2.0, new bool[5])}, {"CP2C2", new CapacitorState(2.0, new bool[5])},
                {"CP3A1", new CapacitorState(3.0, new bool[5])}, {"CP3A2", new CapacitorState(3.0, new bool[5])},
                {"CP3B1", new CapacitorState(3.0, new bool[5])}, {"CP3B2", new CapacitorState(3.0, new bool[5])},
                {"CP3C1", new CapacitorState(3.0, new bool[5])}, {"CP3C2", new CapacitorState(3.0, new bool[5])},
                {"CP4A1", new CapacitorState(4.0, new bool[5])}, {"CP4A2", new CapacitorState(4.0, new bool[5])},
                {"CP4B1", new CapacitorState(4.0, new bool[5])}, {"CP4B2", new CapacitorState(4.0, new bool[5])},
                {"CP4C1", new CapacitorState(4.0, new bool[5])}, {"CP4C2", new CapacitorState(4.0, new bool[5])}
            };
        }
        
        private void InitializeSwitchStates()
        {
            var csSwitches = new[] 
            {
                "CSA", "CSB", "CSC",
                "CS1A1", "CS1A2", "CS2A1", "CS2A2", "CS3A1", "CS3A2", "CS4A1", "CS4A2",
                "CS1B1", "CS1B2", "CS2B1", "CS2B2", "CS3B1", "CS3B2", "CS4B1", "CS4B2",
                "CS1C1", "CS1C2", "CS2C1", "CS2C2", "CS3C1", "CS3C2", "CS4C1", "CS4C2",
                "CS6A", "CS6B", "CS6C", "CS7A", "CS7B", "CS7C"
            };
            foreach (var sw in csSwitches)
            {
                switchStates[sw] = false;
            }
        }
        
        #region UI Event Handlers & Updates

        private void RegisterEventHandlers()
        {
            try
            {
                foreach (var capacitorName in _capacitorStates.Keys)
                {
                    if (FindName(capacitorName) is Image capacitorImage)
                    {
                        capacitorImage.MouseLeftButtonDown += Capacitor_MouseLeftButtonDown;
                        capacitorImage.MouseEnter += Capacitor_MouseEnter;
                        capacitorImage.MouseLeave += Capacitor_MouseLeave;
                    }
                }

                foreach (var switchName in switchStates.Keys)
                {
                    if (FindName(switchName) is Image switchImage)
                    {
                        switchImage.MouseLeftButtonDown += SwitchControl_MouseLeftButtonDown;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[RegisterEventHandlers] Erro ao registrar eventos: {ex.Message}");
            }
        }
        
        private async void BtnSuggest_Click(object sender, RoutedEventArgs e)
        {
            ClearResultsAndLayout();

            var selectedCircuitTypeItem = cmbCircuitType?.SelectedItem as ComboBoxItem;
            var selectedVoltageItem = cmbVoltageLevel?.SelectedItem as ComboBoxItem;

            if (selectedCircuitTypeItem == null || selectedVoltageItem == null)
            {
                MessageBox.Show("Por favor, selecione o tipo de circuito e o nível de tensão.");
                return;
            }

            string circuitType = selectedCircuitTypeItem.Content.ToString();
            string selectedVoltage = selectedVoltageItem.Content.ToString();

            if (!double.TryParse(txtTotalPower.Text, NumberStyles.Any, CultureInfo.InvariantCulture, out double desiredPower))
            {
                MessageBox.Show("Por favor, insira um valor numérico válido para a potência desejada.");
                return;
            }

            Debug.WriteLine($"[BtnSuggest_Click] Iniciando sugestão para {circuitType}, {selectedVoltage}kV, {desiredPower}MVAr.");

            try
            {
                var result = (Success: false, CsConfiguration: "", QConfigurationBest: "", CapacitorsBest: new List<string>(), QConfigurationBelow: "", CapacitorsBelow: new List<string>(), QConfigurationAbove: "", CapacitorsAbove: new List<string>(), ClosestPowerBelow: 0.0, ClosestPowerAbove: 0.0, BestOptionPower: 0.0, MaxPowerGroup1: 0.0);
                
                if (circuitType == "Trifásico")
                {
                    result = await Task.Run(() => _configSuggester.SuggestConfiguration(selectedVoltage, desiredPower, circuitType));
                }
                else // Monofásico
                {
                    // Para monofásico, precisamos de um dicionário para armazenar as combinações individuais
                    var capacitorSwitchCombinations = new Dictionary<string, List<(List<int> Combination, double Power)>>();
                    var configSuggesterMonophase = new ConfigurationSuggesterMonophase();
                    result = configSuggesterMonophase.SuggestConfiguration(selectedVoltage, desiredPower, capacitorSwitchCombinations);

                    // Armazena as configurações individuais para a verificação posterior
                    CapacitorConfigurations.Clear();
                    foreach (var entry in capacitorSwitchCombinations)
                    {
                        var capacitorName = entry.Key;
                        var combinationData = entry.Value.FirstOrDefault(); // Pega a primeira (e única) combinação para este capacitor
                        var qSwitchStates = new bool[5];
                        if (combinationData.Combination != null)
                        {
                            foreach(var switchNum in combinationData.Combination)
                            {
                                if(switchNum >= 1 && switchNum <= 5)
                                    qSwitchStates[switchNum - 1] = true;
                            }
                        }
                        CapacitorConfigurations[capacitorName] = qSwitchStates;
                    }
                }
                
                _suggestedConfiguration = (result.Success, result.CsConfiguration, result.QConfigurationBest, result.CapacitorsBest);
                
                if (result.Success)
                {
                    DisplaySuggestionResults(result, circuitType);
                }
                else
                {
                    MessageBox.Show("Não foi possível encontrar uma configuração para os valores inseridos.");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[BtnSuggest_Click] Exceção: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"Erro ao sugerir configuração: {ex.Message}");
            }
        }

        private async void BtnVerify_Click(object sender, RoutedEventArgs e)
        {
            var selectedCircuitTypeItem = cmbCircuitType?.SelectedItem as ComboBoxItem;
            var selectedVoltageItem = cmbVoltageLevel?.SelectedItem as ComboBoxItem;

            if (selectedCircuitTypeItem == null || selectedVoltageItem == null)
            {
                MessageBox.Show("Selecione o tipo de circuito e nível de tensão.");
                return;
            }
            string circuitType = selectedCircuitTypeItem.Content.ToString();
            string selectedVoltage = selectedVoltageItem.Content.ToString();

            if (!double.TryParse(txtTotalPower?.Text ?? "", out double desiredPower))
            {
                MessageBox.Show("Insira um valor numérico válido para a potência.");
                return;
            }

            VerifyProgressBar.Visibility = Visibility.Visible;
            await Task.Run(() =>
            {
                if (circuitType == "Trifásico")
                {
                    _checkThreePhase.VerifyCircuit(selectedVoltage, desiredPower);
                }
                else // Monofásico
                {
                    _checkMonoPhase.VerifyCircuit(selectedVoltage, desiredPower);
                }
            });
            VerifyProgressBar.Visibility = Visibility.Collapsed;
        }

        public void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearResultsAndLayout();
            InitializeSwitchStates(); // Reseta o estado lógico das chaves
            
            foreach (var switchControl in FindVisualChildren<Image>(MyCanvas))
            {
                if (switchControl.Name.StartsWith("CS"))
                {
                    switchControl.Source = _openImage;
                }
            }
            UpdateLineVisuals();
        }

        private void btnCorrection_Click(object sender, RoutedEventArgs e)
        {
            var correctionHelper = new CorrectionHelper(this);
            correctionHelper.CorrectPower();
        }
        
        private void CmbCircuitType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!IsInitialized || cmbCircuitType.SelectedItem == null) return;

            string circuitType = (cmbCircuitType.SelectedItem as ComboBoxItem)?.Content.ToString();
            
            var voltages = circuitType == "Trifásico"
                ? new List<string> { "13.8", "23.9", "27.6", "41.4", "47.8", "55.2", "71.7", "95.6" }
                : new List<string> { "13.8", "27.6", "41.4", "55.2" };

            cmbVoltageLevel.Items.Clear();
            foreach (string voltage in voltages)
            {
                cmbVoltageLevel.Items.Add(new ComboBoxItem { Content = voltage });
            }
            cmbVoltageLevel.SelectedIndex = 0;
        }

        #endregion

        #region UI Logic and Visuals

        public void ClearResultsAndLayout()
        {
            Dispatcher.Invoke(() =>
            {
                ResultItemsControl.ItemsSource = null;
                TotalPowerLabel.Text = "Total Power: 0 MVAr";
                
                ResetCapacitorConfiguration();

                foreach (var visualChild in FindVisualChildren<Image>(MyCanvas))
                {
                    if (visualChild.Name.StartsWith("CP") || visualChild.Name.StartsWith("CS"))
                    {
                        visualChild.Effect = null;
                        visualChild.OpacityMask = null;
                        visualChild.RenderTransform = null;
                    }
                }
            });
        }
        
        private void DisplaySuggestionResults( (bool Success, string CsConfiguration, string QConfigurationBest, List<string> CapacitorsBest, string QConfigurationBelow, List<string> CapacitorsBelow, string QConfigurationAbove, List<string> CapacitorsAbove, double ClosestPowerBelow, double ClosestPowerAbove, double BestOptionPower, double MaxPowerGroup1) result, string circuitType)
        {
            var resultOptions = new List<string>();

            if(circuitType == "Trifásico")
            {
                 if (result.BestOptionPower > 0)
                    resultOptions.Add($"Melhor opção: {result.QConfigurationBest} | Potência: {result.BestOptionPower:F2} MVAr");
                if (result.ClosestPowerBelow > 0)
                    resultOptions.Add($"Opção Abaixo: {result.QConfigurationBelow} | Potência: {result.ClosestPowerBelow:F2} MVAr");
                if (result.ClosestPowerAbove > 0)
                    resultOptions.Add($"Opção Acima: {result.QConfigurationAbove} | Potência: {result.ClosestPowerAbove:F2} MVAr");
            }
            else //Monofásico
            {
                if(result.BestOptionPower > 0)
                {
                    resultOptions.Add($"Configuração Sugerida | Potência: {result.BestOptionPower:F2} MVAr");
                    resultOptions.Add($"Capacitores: {string.Join(", ", result.CapacitorsBest)}");
                    resultOptions.Add($"Chaves CS: {result.CsConfiguration}");
                }
            }

            ResultItemsControl.ItemsSource = resultOptions;
            
            // Aplica a melhor configuração ao layout
            ApplyConfiguration(result.CsConfiguration, circuitType == "Trifásico" ? result.QConfigurationBest : null);
        }

        // Aplica a configuração ao layout (chaves CS e capacitores)
        public void ApplyConfiguration(string csConfiguration, string qConfiguration)
        {
            ResetCapacitorConfiguration(); // Limpa estado anterior
            
            var csSwitchesToClose = new HashSet<string>(csConfiguration.Split(',').Select(s => s.Trim()));

            // Aplica Chaves CS
            foreach (var switchControl in FindVisualChildren<Image>(MyCanvas))
            {
                if (switchControl.Name.StartsWith("CS"))
                {
                    bool close = csSwitchesToClose.Contains(switchControl.Name);
                    switchControl.Source = close ? _closedImage : _openImage;
                    switchStates[switchControl.Name] = close;
                }
            }

            // Se for trifásico, aplica a mesma config Q para todos os capacitores sugeridos
            if (qConfiguration != null && _suggestedConfiguration.CapacitorsBest != null)
            {
                bool[] qStates = ParseQConfiguration(qConfiguration);
                foreach(var capName in _suggestedConfiguration.CapacitorsBest)
                {
                    if(_capacitorStates.TryGetValue(capName, out var capState))
                    {
                        capState.SwitchStates = qStates;
                        capState.IsModified = qStates.Any(s => s);
                    }
                }
            }
            // Se for monofásico, a configuração já foi aplicada em CapacitorConfigurations
            else if (_suggestedConfiguration.CapacitorsBest != null)
            {
                 foreach(var capName in _suggestedConfiguration.CapacitorsBest)
                {
                    if(_capacitorStates.TryGetValue(capName, out var capState) && CapacitorConfigurations.TryGetValue(capName, out var qStates))
                    {
                        capState.SwitchStates = qStates;
                        capState.IsModified = qStates.Any(s => s);
                    }
                }
            }

            UpdateAllCapacitorAppearances();
            UpdateTotalPowerLabel();
            UpdateLineVisuals();
            HighlightSuggestedComponents();
        }
        
        private bool[] ParseQConfiguration(string qConfig)
        {
            var states = new bool[5];
            if (string.IsNullOrEmpty(qConfig)) return states;

            var qParts = qConfig.Split(',');
            foreach (var part in qParts)
            {
                if (int.TryParse(part.Trim().Replace("Q", ""), out int index) && index >= 1 && index <= 5)
                {
                    states[index - 1] = true;
                }
            }
            return states;
        }

        private void HighlightSuggestedComponents()
        {
            if (!_suggestedConfiguration.Success) return;

            foreach (var capName in _suggestedConfiguration.CapacitorsBest)
            {
                HighlightCapacitor(capName, true);
            }
            var csSwitches = _suggestedConfiguration.CsConfiguration.Split(',').Select(s => s.Trim());
            foreach (var swName in csSwitches)
            {
                HighlightSwitch(swName, true);
            }
        }

        internal void HighlightCapacitor(string capacitorName, bool isCorrect)
        {
            if (FindName(capacitorName) is Image capacitorImage)
            {
                capacitorImage.Effect = new DropShadowEffect
                {
                    Color = isCorrect ? Colors.DeepSkyBlue : Colors.Red,
                    ShadowDepth = 0, BlurRadius = 15, Opacity = 0.9
                };
            }
        }

        internal void HighlightSwitch(string switchName, bool isCorrect)
        {
            if (FindName(switchName) is Image switchImage)
            {
                 switchImage.Effect = new DropShadowEffect
                {
                    Color = isCorrect ? Colors.DeepSkyBlue : Colors.Red,
                    ShadowDepth = 0, BlurRadius = 15, Opacity = 0.9
                };
            }
        }
        
        private void UpdateAllCapacitorAppearances()
        {
            foreach (var (capacitorName, state) in _capacitorStates)
            {
                if (FindName(capacitorName) is Image capacitorImage)
                {
                    UpdateCapacitorAppearance(capacitorImage, state.IsModified);
                }
            }
        }
        
        private void UpdateCapacitorAppearance(Image capacitorImage, bool isModified)
        {
            if (isModified)
            {
                if (_capacitorStates.TryGetValue(capacitorImage.Name, out CapacitorState state))
                {
                    double power = CalculateQPower(state.SwitchStates);
                    Color color = _colorManager.GetColorForPower(power);
                    
                    capacitorImage.Effect = new DropShadowEffect
                    {
                        Color = color, Direction = 0, ShadowDepth = 0, BlurRadius = 20, Opacity = 1
                    };

                    if (capacitorImage.RenderTransform is not ScaleTransform)
                    {
                        var scaleTransform = new ScaleTransform(1.0, 1.0);
                        capacitorImage.RenderTransformOrigin = new Point(0.5, 0.5);
                        capacitorImage.RenderTransform = scaleTransform;

                        var scaleAnimation = new DoubleAnimation(1.0, 1.05, TimeSpan.FromSeconds(0.7))
                        {
                            AutoReverse = true, RepeatBehavior = RepeatBehavior.Forever
                        };
                        scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                        scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);
                    }
                }
            }
            else
            {
                capacitorImage.Effect = null;
                if (capacitorImage.RenderTransform is ScaleTransform st)
                {
                    st.BeginAnimation(ScaleTransform.ScaleXProperty, null);
                    st.BeginAnimation(ScaleTransform.ScaleYProperty, null);
                }
                capacitorImage.RenderTransform = null;
            }
        }
        
        private void SwitchControl_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Image switchImage && switchImage.Name.StartsWith("CS"))
            {
                bool isNowClosed = switchImage.Source == _openImage;
                switchImage.Source = isNowClosed ? _closedImage : _openImage;
                switchStates[switchImage.Name] = isNowClosed;
                UpdateLineVisuals();
            }
        }
        
        private void Capacitor_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is FrameworkElement element && e.ClickCount == 1)
            {
                var capIndividualPopup = new Cap_Individual(
                    element.Name,
                    _capacitorStates[element.Name].Power,
                    (bool[])_capacitorStates[element.Name].SwitchStates.Clone(),
                    _capacitorStates
                );
                capIndividualPopup.CapacitorPowerSelected += CapIndividualPopup_CapacitorPowerSelected;
                capIndividualPopup.ShowDialog();
            }
        }

        private void CapIndividualPopup_CapacitorPowerSelected(string capacitorName, bool[] newSwitchStates, double power)
        {
            if (_capacitorStates.TryGetValue(capacitorName, out var state))
            {
                state.SwitchStates = newSwitchStates;
                state.Power = power;
                state.IsModified = newSwitchStates.Any(s => s);

                if (FindName(capacitorName) is Image capacitorImage)
                {
                    UpdateCapacitorAppearance(capacitorImage, state.IsModified);
                }
                UpdateTotalPowerLabel();
            }
        }
        
        private void UpdateTotalPowerLabel()
        {
            double totalPower = _capacitorStates.Values.Where(s => s.IsModified).Sum(state => CalculationHelper.CalculateQPower(state.SwitchStates));
            TotalPowerLabel.Text = $"Total Power: {totalPower:F2} MVAr";
        }
        
        public void ResetCapacitorConfiguration()
        {
            Dispatcher.Invoke(() =>
            {
                foreach (var state in _capacitorStates.Values)
                {
                    state.SwitchStates = new bool[5];
                    state.IsSelected = false;
                    state.IsModified = false;
                    state.Power = 0;
                }
                UpdateAllCapacitorAppearances();
                UpdateTotalPowerLabel();
            });
        }
        
        #endregion
        
        #region Utility Methods

        

        private bool IsAnyKeyActive(params string[] keys)
        {
            return keys.Any(key => switchStates.ContainsKey(key) && switchStates[key]);
        }
        
        internal static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child is T t)
                    {
                        yield return t;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }
        
        public void UpdateResultText(string messages)
        {
            MessageBox.Show(messages, "Resultados da Verificação", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Capacitor_MouseEnter(object sender, MouseEventArgs e)
        {
            if (sender is Image capacitorImage)
            {
                if (_capacitorStates.TryGetValue(capacitorImage.Name, out CapacitorState state))
                {
                    double power = CalculateQPower(state.SwitchStates);
                    capacitorImage.ToolTip = new ToolTip { Content = $"{power:F2} MVAr" };
                }
            }
        }

        private void Capacitor_MouseLeave(object sender, MouseEventArgs e)
        {
            if (sender is Image capacitorImage)
            {
                if (capacitorImage.ToolTip is ToolTip tt)
                {
                    tt.IsOpen = false;
                    capacitorImage.ToolTip = null;
                }
            }
        }
        
        // A lógica de UpdateLineVisuals foi refatorada para maior clareza.
        private void UpdateLineVisuals()
        {
            UpdatePhaseAVisuals();
            UpdatePhaseBVisuals();
            UpdatePhaseCVisuals();
            UpdateInterPhaseVisuals();
        }

        private void UpdatePhaseAVisuals()
        {
            try
            {
                var FASE_A_SAIDA_TC_1 = FindName("FASE_A_SAIDA_TC_1") as Line;
                var FASE_A_BARRA_ENTRADA_CS_2A1_E_CS_2A2 = FindName("FASE_A_BARRA_ENTRADA_CS_2A1_E_CS_2A2") as Line;
                var FASE_A_SAIDA_CS_2A1 = FindName("FASE_A_SAIDA_CS_2A1") as Line;
                var FASE_A_SAIDA_CS_4A1 = FindName("FASE_A_SAIDA_CS_4A1") as Line;
                var FASE_A_ENTRADA_CS_1A1 = FindName("FASE_A_ENTRADA_CS_1A1") as Line;
                var FASE_A_BARRA_SAIDA_CS_3A1_ATE_CS_1A2 = FindName("FASE_A_BARRA_SAIDA_CS_3A1_ATE_CS_1A2") as Line;
                var FASE_A_SAIDA_CS_3A1 = FindName("FASE_A_SAIDA_CS_3A1") as Line;
                var FASE_A_ENTRADA_CS_7A = FindName("FASE_A_ENTRADA_CS_7A") as Line;
                var FASE_A_ENTRADA_CS_6A = FindName("FASE_A_ENTRADA_CS_6A") as Line;
                var FASE_A_SAIDA_CP_1A1 = FindName("FASE_A_SAIDA_CP_1A1") as Line;
                var FASE_A_ENTRADA_CS_3A2 = FindName("FASE_A_ENTRADA_CS_3A2") as Line;
                var FASE_A_ENTRADA_CS_1A2 = FindName("FASE_A_ENTRADA_CS_1A2") as Line;
                var FASE_A_SAIDA_CS_2A2 = FindName("FASE_A_SAIDA_CS_2A2") as Line;
                var FASE_A_SAIDA_CS_4A2 = FindName("FASE_A_SAIDA_CS_4A2") as Line;
                var FASE_A_ENTRADA_CS_4A2 = FindName("FASE_A_ENTRADA_CS_4A2") as Line;

                if (FASE_A_SAIDA_TC_1 != null) FASE_A_SAIDA_TC_1.Stroke = IsAnyKeyActive("CSA") ? Brushes.Red : Brushes.Black;
                if (FASE_A_BARRA_ENTRADA_CS_2A1_E_CS_2A2 != null) FASE_A_BARRA_ENTRADA_CS_2A1_E_CS_2A2.Stroke = IsAnyKeyActive("CSA") ? Brushes.Red : Brushes.Black;
                if (FASE_A_SAIDA_CS_2A1 != null) FASE_A_SAIDA_CS_2A1.Stroke = IsAnyKeyActive("CS2A1") ? Brushes.Red : Brushes.Black;
                if (FASE_A_SAIDA_CS_4A1 != null) FASE_A_SAIDA_CS_4A1.Stroke = IsAnyKeyActive("CS4A1") ? Brushes.Red : Brushes.Black;
                if (FASE_A_ENTRADA_CS_1A1 != null) FASE_A_ENTRADA_CS_1A1.Stroke = IsAnyKeyActive("CS2A1", "CS4A1") ? Brushes.Red : Brushes.Black;
                if (FASE_A_BARRA_SAIDA_CS_3A1_ATE_CS_1A2 != null) FASE_A_BARRA_SAIDA_CS_3A1_ATE_CS_1A2.Stroke = IsAnyKeyActive("CS3A1", "CS1A1") ? Brushes.Red : Brushes.Black;
                if (FASE_A_SAIDA_CS_3A1 != null) FASE_A_SAIDA_CS_3A1.Stroke = IsAnyKeyActive("CS3A1") ? Brushes.Red : Brushes.Black;
                if (FASE_A_ENTRADA_CS_7A != null) FASE_A_ENTRADA_CS_7A.Stroke = IsAnyKeyActive("CS1A1", "CS3A1", "CS3A2", "CS1A2", "CS2A2", "CS4A2") ? Brushes.Red : Brushes.Black;
                if (FASE_A_ENTRADA_CS_6A != null) FASE_A_ENTRADA_CS_6A.Stroke = IsAnyKeyActive("CS1A1", "CS3A1", "CS3A2", "CS1A2", "CS2A2", "CS4A2") ? Brushes.Red : Brushes.Black;
                if (FASE_A_SAIDA_CP_1A1 != null) FASE_A_SAIDA_CP_1A1.Stroke = IsAnyKeyActive("CS1A1") ? Brushes.Red : Brushes.Black;
                if (FASE_A_ENTRADA_CS_3A2 != null) FASE_A_ENTRADA_CS_3A2.Stroke = IsAnyKeyActive("CSA") ? Brushes.Red : Brushes.Black;
                if (FASE_A_ENTRADA_CS_1A2 != null) FASE_A_ENTRADA_CS_1A2.Stroke = IsAnyKeyActive("CS3A1", "CS1A1", "CS3A2") ? Brushes.Red : Brushes.Black;
                if (FASE_A_SAIDA_CS_2A2 != null) FASE_A_SAIDA_CS_2A2.Stroke = IsAnyKeyActive("CS2A2") ? Brushes.Red : Brushes.Black;
                if (FASE_A_SAIDA_CS_4A2 != null) FASE_A_SAIDA_CS_4A2.Stroke = IsAnyKeyActive("CS4A2") ? Brushes.Red : Brushes.Black;
                if (FASE_A_ENTRADA_CS_4A2 != null) FASE_A_ENTRADA_CS_4A2.Stroke = IsAnyKeyActive("CSA") ? Brushes.Red : Brushes.Black;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[UpdatePhaseAVisuals] Erro: {ex.Message}");
            }
        }

        private void UpdatePhaseBVisuals()
        {
            if (FASE_B_SAIDA_TC_2 != null) FASE_B_SAIDA_TC_2.Stroke = IsAnyKeyActive("CSB") ? Brushes.Green : Brushes.Black;
            if (FASE_B_BARRA_ENTRADA_CS_2B1_ATE_CS_2B2 != null) FASE_B_BARRA_ENTRADA_CS_2B1_ATE_CS_2B2.Stroke = IsAnyKeyActive("CSB") ? Brushes.Green : Brushes.Black;
            if (FASE_B_SAIDA_CS_2B1 != null) FASE_B_SAIDA_CS_2B1.Stroke = IsAnyKeyActive("CS2B1") ? Brushes.Green : Brushes.Black;
            if (FASE_B_SAIDA_CS_4B1 != null) FASE_B_SAIDA_CS_4B1.Stroke = IsAnyKeyActive("CS4B1") ? Brushes.Green : Brushes.Black;
            if (FASE_B_ENTRADA_CS_1B1 != null) FASE_B_ENTRADA_CS_1B1.Stroke = IsAnyKeyActive("CS2B1", "CS4B1") ? Brushes.Green : Brushes.Black;
            if (FASE_B_BARRA_SAIDA_CS_3B1_A_CS_1B2 != null) FASE_B_BARRA_SAIDA_CS_3B1_A_CS_1B2.Stroke = IsAnyKeyActive("CS3B1", "CS1B1") ? Brushes.Green : Brushes.Black;
            if (FASE_B_SAIDA_CS_3B1 != null) FASE_B_SAIDA_CS_3B1.Stroke = IsAnyKeyActive("CS3B1") ? Brushes.Green : Brushes.Black;
            if (FASE_B_ENTRADA_CS_7B != null) FASE_B_ENTRADA_CS_7B.Stroke = IsAnyKeyActive("CS1B1", "CS3B1", "CS1B2", "CS3B2", "CS2B2", "CS4B2") ? Brushes.Green : Brushes.Black;
            if (FASE_B_ENTRADA_CS_6B != null) FASE_B_ENTRADA_CS_6B.Stroke = IsAnyKeyActive("CS1B1", "CS3B1", "CS1B2", "CS3B2", "CS2B2", "CS4B2") ? Brushes.Green : Brushes.Black;
            if (FASE_B_SAIDA_CP_1B1 != null) FASE_B_SAIDA_CP_1B1.Stroke = IsAnyKeyActive("CS1B1") ? Brushes.Green : Brushes.Black;
            if (FASE_B_ENTRADA_CS_3B2 != null) FASE_B_ENTRADA_CS_3B2.Stroke = IsAnyKeyActive("CSB") ? Brushes.Green : Brushes.Black;
            if (FASE_B_ENTRADA_CS_1B2 != null) FASE_B_ENTRADA_CS_1B2.Stroke = IsAnyKeyActive("CS3B1", "CS1B1", "CS3B2") ? Brushes.Green : Brushes.Black;
            if (FASE_B_SAIDA_CS_2B2 != null) FASE_B_SAIDA_CS_2B2.Stroke = IsAnyKeyActive("CS2B2") ? Brushes.Green : Brushes.Black;
            if (FASE_B_SAIDA_CS_4B2 != null) FASE_B_SAIDA_CS_4B2.Stroke = IsAnyKeyActive("CS4B2") ? Brushes.Green : Brushes.Black;
            if (FASE_B_ENTRADA_CS_4B2_ != null) FASE_B_ENTRADA_CS_4B2_.Stroke = IsAnyKeyActive("CSB") ? Brushes.Green : Brushes.Black;
        }

        private void UpdatePhaseCVisuals()
        {
            if (FASE_C_SAIDA_TC_3 != null) FASE_C_SAIDA_TC_3.Stroke = IsAnyKeyActive("CSC") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_BARRA_ENTRADA_CS_2C1_E_CS_2C2 != null) FASE_C_BARRA_ENTRADA_CS_2C1_E_CS_2C2.Stroke = IsAnyKeyActive("CSC") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_SAIDA_2C1 != null) FASE_C_SAIDA_2C1.Stroke = IsAnyKeyActive("CS2C1") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_SAIDA_CS_4C1 != null) FASE_C_SAIDA_CS_4C1.Stroke = IsAnyKeyActive("CS4C1") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_ENTRADA_CS_1C1 != null) FASE_C_ENTRADA_CS_1C1.Stroke = IsAnyKeyActive("CS2C1", "CS4C1") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_BARRA_SAIDA_CS_3C1_A_CS_1C2 != null) FASE_C_BARRA_SAIDA_CS_3C1_A_CS_1C2.Stroke = IsAnyKeyActive("CS3C1", "CS1C1") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_SAIDA_CS_3C1 != null) FASE_C_SAIDA_CS_3C1.Stroke = IsAnyKeyActive("CS3C1") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_ENTRADA_CS_7C != null) FASE_C_ENTRADA_CS_7C.Stroke = IsAnyKeyActive("CS1C1", "CS3C1", "CS1C2", "CS3C2", "CS2C2", "CS4C2") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_SAIDA_CP_1C1 != null) FASE_C_SAIDA_CP_1C1.Stroke = IsAnyKeyActive("CS1C1") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_ENTRADA_CS_3C2 != null) FASE_C_ENTRADA_CS_3C2.Stroke = IsAnyKeyActive("CSC") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_ENTRADA_CS_1C2 != null) FASE_C_ENTRADA_CS_1C2.Stroke = IsAnyKeyActive("CS3C1", "CS1C1", "CS3C2") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_SAIDA_CS_2C2 != null) FASE_C_SAIDA_CS_2C2.Stroke = IsAnyKeyActive("CS2C2") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_SAIDA_CS_4C2 != null) FASE_C_SAIDA_CS_4C2.Stroke = IsAnyKeyActive("CS4C2") ? Brushes.Blue : Brushes.Black;
            if (FASE_C_ENTRADA_CS_4C2 != null) FASE_C_ENTRADA_CS_4C2.Stroke = IsAnyKeyActive("CSC") ? Brushes.Blue : Brushes.Black;
        }

        private void UpdateInterPhaseVisuals()
        {
            if (FASE_C_BARRA_SAIDA_CS_7C_ATE_CP_4A2 != null) FASE_C_BARRA_SAIDA_CS_7C_ATE_CP_4A2.Stroke = IsAnyKeyActive("CS7C") ? Brushes.Red : Brushes.Black;
            if (FASE_C_SAIDA_CS_7C != null) FASE_C_SAIDA_CS_7C.Stroke = IsAnyKeyActive("CS7C") ? Brushes.Red : Brushes.Black;
            if (FASE_A_BARRA_SAIDA_CS_7A != null) FASE_A_BARRA_SAIDA_CS_7A.Stroke = IsAnyKeyActive("CS7A") ? Brushes.Green : Brushes.Black;
            if (FASE_A_SAIDA_CS_7A != null) FASE_A_SAIDA_CS_7A.Stroke = IsAnyKeyActive("CS7A") ? Brushes.Green : Brushes.Black;
            if (FASE_B_BARRA_SAIDA_CS_7B != null) FASE_B_BARRA_SAIDA_CS_7B.Stroke = IsAnyKeyActive("CS7B") ? Brushes.Blue : Brushes.Black;
            if (FASE_B_SAIDA_7B != null) FASE_B_SAIDA_7B.Stroke = IsAnyKeyActive("CS7B") ? Brushes.Blue : Brushes.Black;

            if (FASE_C_SAIDA_CS_6B != null) FASE_C_SAIDA_CS_6B.Stroke = IsAnyKeyActive("CS6B") ? Brushes.Blue : Brushes.Black;
            if (FASE_A_SAIDA_CS_6A != null) FASE_A_SAIDA_CS_6A.Stroke = IsAnyKeyActive("CS6A") ? Brushes.Green : Brushes.Black;
            if (BARRA_DE_NEUTRO__ENTRADA_CS_C6 != null) BARRA_DE_NEUTRO__ENTRADA_CS_C6.Stroke = IsAnyKeyActive("CS6C") ? Brushes.Blue : Brushes.Black;
        }
        
        #endregion
    }
}