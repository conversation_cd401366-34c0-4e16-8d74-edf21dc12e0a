{"Version": 1, "WorkspaceRootPath": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|d:\\c#\\capbankautomation\\capbankautomation rev1.8\\cap_individual.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:cap_individual.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\capacitorcolormanager.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:capacitorcolormanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\cap_individual.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:cap_individual.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\configurationsuggester.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:configurationsuggester.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\checkmonophase.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:checkmonophase.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\checkthreephase.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:checkthreephase.xaml.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\correctionhelper.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{576525AF-F20E-4E17-ABCF-202E0E413B10}|CapBankAutomation.csproj|solutionrelative:correctionhelper.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "CapacitorColorManager.cs", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CapacitorColorManager.cs", "RelativeDocumentMoniker": "CapacitorColorManager.cs", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CapacitorColorManager.cs", "RelativeToolTip": "CapacitorColorManager.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-04T14:11:00.628Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{b1e99781-ab81-11d0-b683-00aa00a3ee26}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:1:0:{f2bd8fb8-fc94-3dae-a733-fd993c73cc87}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Cap_Individual.xaml", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\Cap_Individual.xaml", "RelativeDocumentMoniker": "Cap_Individual.xaml", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\Cap_Individual.xaml", "RelativeToolTip": "Cap_Individual.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-02-04T15:02:39.706Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Cap_Individual.xaml.cs", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\Cap_Individual.xaml.cs", "RelativeDocumentMoniker": "Cap_Individual.xaml.cs", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\Cap_Individual.xaml.cs", "RelativeToolTip": "Cap_Individual.xaml.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAH4AAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-13T19:01:28.482Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AQIAACcGAAAAAAAAAABAwEcGAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-13T18:40:51.085Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "App.xaml", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\App.xaml", "RelativeDocumentMoniker": "App.xaml", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\App.xaml", "RelativeToolTip": "App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-02-04T15:02:26.85Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "CorrectionHelper.cs", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CorrectionHelper.cs", "RelativeDocumentMoniker": "CorrectionHelper.cs", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CorrectionHelper.cs", "RelativeToolTip": "CorrectionHelper.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-04T14:29:33.868Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ConfigurationSuggester.cs", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\ConfigurationSuggester.cs", "RelativeDocumentMoniker": "ConfigurationSuggester.cs", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\ConfigurationSuggester.cs", "RelativeToolTip": "ConfigurationSuggester.cs", "ViewState": "AQIAACcBAAAAAAAAAAAUwHoBAAAJAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-04T14:29:32.799Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-02-04T15:02:48.574Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "CheckThreePhase.xaml.cs", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CheckThreePhase.xaml.cs", "RelativeDocumentMoniker": "CheckThreePhase.xaml.cs", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CheckThreePhase.xaml.cs", "RelativeToolTip": "CheckThreePhase.xaml.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-04T14:29:31.677Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CheckMonoPhase.xaml.cs", "DocumentMoniker": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CheckMonoPhase.xaml.cs", "RelativeDocumentMoniker": "CheckMonoPhase.xaml.cs", "ToolTip": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CheckMonoPhase.xaml.cs", "RelativeToolTip": "CheckMonoPhase.xaml.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-04T14:10:56.101Z", "EditorCaption": ""}]}]}]}