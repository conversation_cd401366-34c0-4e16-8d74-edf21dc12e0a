﻿using System.Collections.Generic;
using System.Threading.Tasks;

namespace CapBankAutomation
{
    public class CheckMonophase : CheckCircuitBase
    {
        public CheckMonophase(MainWindow mainWindow) : base(mainWindow)
        {
        }
        
        public async Task SuggestConfigurationAsync(string voltage, double desiredPower, Dictionary<string, List<(List<int> Combination, double Power)>> capacitorSwitchCombinations)
        {
            var configSuggesterMonophase = new ConfigurationSuggesterMonophase();
            var result = await Task.Run(() => configSuggesterMonophase.SuggestConfiguration(voltage, desiredPower, capacitorSwitchCombinations));
            
            // Armazena a sugestão completa na MainWindow para uso posterior na verificação.
            _mainWindow._suggestedConfiguration = result;
             
            if (result.Success)
            {
                 // A aplicação da configuração no UI será feita pelo usuário ou por um botão de "aplicar"
                 // Esta classe foca na sugestão e verificação.
            }
        }

        /// <summary>
        /// Para o modo monofásico, cada capacitor pode ter uma configuração de chave Q diferente.
        /// A configuração é armazenada em `_mainWindow.CapacitorConfigurations` durante a sugestão.
        /// </summary>
        protected override bool[] GetExpectedQSwitchStates(string capacitorName, (bool Success, string CsConfiguration, string QConfigurationBest, List<string> CapacitorsBest) suggestedConfig)
        {
            // No modo monofásico, a configuração de cada capacitor é individual.
            // Ela é armazenada no dicionário `CapacitorConfigurations` da MainWindow durante a sugestão.
            if (_mainWindow.CapacitorConfigurations.TryGetValue(capacitorName, out bool[] switchStates))
            {
                return switchStates;
            }

            // Se não encontrar, retorna um array de chaves abertas.
            return new bool[5];
        }
    }
}