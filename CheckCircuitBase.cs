﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Controls;

namespace CapBankAutomation
{
    /// <summary>
    /// Classe base abstrata para verificação de circuitos, contendo lógica comum
    /// para os modos trifásico e monofásico.
    /// </summary>
    public abstract class CheckCircuitBase
    {
        protected readonly MainWindow _mainWindow;

        protected CheckCircuitBase(MainWindow mainWindow)
        {
            _mainWindow = mainWindow;
        }

        /// <summary>
        /// Método abstrato para obter os estados esperados das chaves Q para um capacitor.
        /// A implementação deve ser fornecida pelas classes filhas.
        /// </summary>
        protected abstract bool[] GetExpectedQSwitchStates(string capacitorName, (bool Success, string CsConfiguration, string QConfigurationBest, List<string> CapacitorsBest) suggestedConfig);

        /// <summary>
        /// Verifica o circuito montado pelo usuário contra a configuração sugerida.
        /// </summary>
        public void VerifyCircuit(string selectedVoltage, double desiredPower)
        {
            Console.WriteLine($"[VerifyCircuit] Iniciando análise do circuito ({this.GetType().Name})...");

            var suggestedConfig = _mainWindow._suggestedConfiguration;
            if (!suggestedConfig.Success || string.IsNullOrEmpty(suggestedConfig.CsConfiguration) || suggestedConfig.CapacitorsBest == null)
            {
                _mainWindow.Dispatcher.Invoke(() =>
                {
                    _mainWindow.UpdateResultText("Erro: A configuração sugerida é inválida ou não foi gerada.");
                });
                return;
            }

            Console.WriteLine($"[VerifyCircuit] Potência desejada: {desiredPower} MVAr, Tensão: {selectedVoltage}");
            Console.WriteLine($"[VerifyCircuit] Chaves CS Sugeridas: {suggestedConfig.CsConfiguration}");
            Console.WriteLine($"[VerifyCircuit] Capacitores Sugeridos: {string.Join(", ", suggestedConfig.CapacitorsBest)}");
            Console.WriteLine($"[VerifyCircuit] Configuração Q Sugerida: {suggestedConfig.QConfigurationBest}");

            var resultMessages = new List<string>();
            var suggestedCsSwitches = new HashSet<string>(suggestedConfig.CsConfiguration.Split(',').Select(s => s.Trim()));
            var suggestedCapacitors = new HashSet<string>(suggestedConfig.CapacitorsBest);

            // 1. Verificar todas as chaves CS no layout
            _mainWindow.Dispatcher.Invoke(() =>
            {
                foreach (var control in MainWindow.FindVisualChildren<Image>(_mainWindow))
                {
                    if (control.Name.StartsWith("CS"))
                    {
                        bool isClosed = control.Source == _mainWindow._closedImage;
                        bool shouldBeClosed = suggestedCsSwitches.Contains(control.Name);

                        if (isClosed && !shouldBeClosed)
                        {
                            _mainWindow.HighlightSwitch(control.Name, false); // Vermelho
                            resultMessages.Add($"A chave {control.Name} está fechada incorretamente.");
                        }
                        else if (!isClosed && shouldBeClosed)
                        {
                            _mainWindow.HighlightSwitch(control.Name, false); // Vermelho
                            resultMessages.Add($"A chave {control.Name} deveria estar fechada.");
                        }
                        else
                        {
                           // Se está correta (fechada e deveria estar, ou aberta e deveria estar), remove o highlight ou deixa azul.
                           _mainWindow.HighlightSwitch(control.Name, true); 
                        }
                    }
                }
            });

            // 2. Verificar os capacitores que DEVERIAM estar no layout
            foreach (var capacitorName in suggestedCapacitors)
            {
                _mainWindow.Dispatcher.Invoke(() =>
                {
                    if (_mainWindow._capacitorStates.TryGetValue(capacitorName, out CapacitorState state))
                    {
                        bool[] currentQSwitchStates = state.SwitchStates;
                        bool[] expectedQSwitchStates = GetExpectedQSwitchStates(capacitorName, suggestedConfig);

                        bool allSwitchesOpen = currentQSwitchStates.All(s => !s);
                        bool isConfiguredCorrectly = Enumerable.SequenceEqual(currentQSwitchStates, expectedQSwitchStates);

                        if (allSwitchesOpen)
                        {
                            resultMessages.Add($"O capacitor {capacitorName} não foi configurado (chaves Q abertas).");
                            _mainWindow.HighlightCapacitor(capacitorName, false); // Vermelho
                        }
                        else if (isConfiguredCorrectly)
                        {
                           _mainWindow.HighlightCapacitor(capacitorName, true); // Azul
                        }
                        else
                        {
                            resultMessages.Add($"O capacitor {capacitorName} está configurado incorretamente.");
                            _mainWindow.HighlightCapacitor(capacitorName, false); // Vermelho
                        }
                    }
                    else
                    {
                        resultMessages.Add($"Erro: Estado do capacitor {capacitorName} não encontrado.");
                    }
                });
            }

            // 3. Verificar capacitores que NÃO DEVERIAM estar no layout
            _mainWindow.Dispatcher.Invoke(() =>
            {
                var allCapacitorNames = _mainWindow._capacitorStates.Keys;
                var unexpectedCapacitors = allCapacitorNames.Except(suggestedCapacitors);

                foreach (var capacitorName in unexpectedCapacitors)
                {
                    if (_mainWindow._capacitorStates.TryGetValue(capacitorName, out CapacitorState state))
                    {
                        bool anySwitchClosed = state.SwitchStates.Any(s => s);
                        if (anySwitchClosed)
                        {
                            _mainWindow.HighlightCapacitor(capacitorName, false); // Vermelho
                            resultMessages.Add($"O capacitor {capacitorName} não deveria estar no layout, mas está ativo.");
                        }
                    }
                }
            });

            _mainWindow.Dispatcher.Invoke(() =>
            {
                if (resultMessages.Count == 0)
                {
                    _mainWindow.UpdateResultText("Parabéns! O circuito está configurado corretamente.");
                }
                else
                {
                    _mainWindow.UpdateResultText(string.Join(Environment.NewLine, resultMessages.Distinct()));
                }
            });

            Console.WriteLine("[VerifyCircuit] Análise do circuito concluída.");
        }
    }
}