{"version": 3, "targets": {".NETFramework,Version=v4.8": {}, ".NETFramework,Version=v4.8/win": {}, ".NETFramework,Version=v4.8/win-arm64": {}, ".NETFramework,Version=v4.8/win-x64": {}, ".NETFramework,Version=v4.8/win-x86": {}}, "libraries": {}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": []}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CapBankAutomation.csproj", "projectName": "CapBankAutomation", "projectPath": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\CapBankAutomation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#\\CapBankAutomation\\CapBankAutomation Rev1.8\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}