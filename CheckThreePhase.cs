using System.Linq;

namespace CapBankAutomation
{
    public class CheckThreePhase : CheckCircuitBase
    {
        public CheckThreePhase(MainWindow mainWindow) : base(mainWindow)
        {
        }

        protected override bool[] GetExpectedQSwitchStates(string capacitorName, (bool Success, string CsConfiguration, string QConfigurationBest, List<string> CapacitorsBest) suggestedConfig)
        {
            var qStates = new bool[5];
            if (string.IsNullOrEmpty(suggestedConfig.QConfigurationBest)) return qStates;

            var qParts = suggestedConfig.QConfigurationBest.Split(',');
            foreach (var part in qParts)
            {
                if (int.TryParse(part.Trim().Replace("Q", ""), out int index) && index >= 1 && index <= 5)
                {
                    qStates[index - 1] = true;
                }
            }
            return qStates;
        }
    }
}
